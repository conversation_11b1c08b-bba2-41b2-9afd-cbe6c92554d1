// Minimal voice call service

export const startVoiceCall = async (userId: string) => {
  console.log(`Starting voice call with ${userId}`);
  return true;
};

export const useVoiceCall = (contactId: string, contactName: string, contactAvatar: string) => {
  // Update callState to be a string enum value instead of an object
  const callState = "connecting" as "connecting" | "ringing" | "ongoing" | "ended";

  const makeCall = async () => {
    console.log(`Making voice call to ${contactId}`);
    return true;
  };

  const acceptCall = () => {
    console.log(`Accepting voice call from ${contactId}`);
  };

  const endCall = () => {
    console.log(`Ending voice call with ${contactId}`);
  };

  const toggleMute = () => {
    console.log(`Toggling mute for voice call with ${contactId}`);
  };

  const toggleSpeaker = () => {
    console.log(`Toggling speaker for voice call with ${contactId}`);
  };

  const formatDuration = () => {
    return '00:00';
  };

  return {
    callState,
    makeCall,
    acceptCall,
    endCall,
    toggleMute,
    toggleSpeaker,
    formatDuration
  };
};

