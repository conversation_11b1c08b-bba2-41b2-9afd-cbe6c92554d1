


import FontAwesome from '@expo/vector-icons/FontAwesome';
import { BlurView } from 'expo-blur';
import Constants from 'expo-constants';
import * as ImagePicker from 'expo-image-picker';
import { LinearGradient } from 'expo-linear-gradient';
import { useLocalSearchParams, useNavigation, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import {
  Alert,
  Animated,
  Dimensions,
  Image,
  Linking,
  Modal,
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  Switch,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { IconSymbol } from '../../components/ui/IconSymbol';
import { Colors } from '../../constants/Colors';
import { useAuth } from '../../context/AuthContext';
import { supabase } from '../../lib/supabase';
import {
  CoinPackage,
  coinPackages,
  handleSuccessfulPayment,
  verifyUpiPayment
} from '../../services/paymentService';

interface SocialMediaLinks {
  instagram?: string;
  facebook?: string;
}

interface UserProfile {
  displayName: string;
  email: string;
  age: number;
  location: string;
  bio: string;
  photos: string[];
  interests: string[];
  occupation: string;
  education: string;
  heatCoins: number;
  preference: string;
  socialMedia?: SocialMediaLinks;
}

const { width, height } = Dimensions.get('window');
const isIOS = Platform.OS === 'ios';

export default function ProfileScreen() {
  const router = useRouter();
  const navigation = useNavigation();
  const params = useLocalSearchParams();
  const { user, logout } = useAuth();
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [showBuyCoinsModal, setShowBuyCoinsModal] = useState(false);
  const [showPhotosModal, setShowPhotosModal] = useState(false);

  const [loading, setLoading] = useState(false);
  const [coinBalance, setCoinBalance] = useState(0);
  const scrollY = new Animated.Value(0);
  
  // Add state for auto-pay
  const [autoPay, setAutoPay] = useState(false);
  
  // Add state to track when preference was last changed
  const [preferenceChangedDate, setPreferenceChangedDate] = useState<Date | null>(null);
  
  // Mock user data - in a real app, this would come from your auth context
  const [userData, setUserData] = useState<UserProfile>({
    displayName: user?.displayName || 'John Doe',
    email: user?.email || '<EMAIL>',
    age: 28,
    location: 'San Francisco, CA',
    bio: user?.bio || 'I love exploring new places, trying different cuisines, and meeting interesting people. Looking for genuine connections and meaningful conversations.',
    photos: user?.photos || [],
    interests: ['Photography', 'Hiking', 'Coffee', 'Travel', 'Reading', 'Music', 'Cooking'],
    occupation: 'Software Engineer',
    education: 'Stanford University',
    heatCoins: 0, // Default to 0 coins
    preference: user?.preference || 'Everyone',
    socialMedia: user?.socialMedia || { instagram: '', facebook: '' } // Add socialMedia property
  });
  
  // Add state for user photos
  const [userPhotos, setUserPhotos] = useState(userData.photos || []);

  useEffect(() => {
    setUserPhotos(userData.photos || []);
  }, [userData.photos]);
  
  // Add state for the photos page container
  const [showPhotosPage, setShowPhotosPage] = useState(false);
  const [instagramLink, setInstagramLink] = useState(userData.socialMedia?.instagram || '');
  const [facebookLink, setFacebookLink] = useState(userData.socialMedia?.facebook || '');

  const handleSaveSocialMedia = async () => {
    if (!user?.id) {
      Alert.alert('Error', 'User not logged in.');
      return;
    }

    setLoading(true);
    const { data, error } = await supabase
      .from('profiles')
      .upsert(
        {
          id: user.id,
          socialMedia: { instagram: instagramLink, facebook: facebookLink },
          photos: userData.photos, // Include photos in the upsert operation
        },
        { onConflict: 'id' }
      )
      .select();

    setLoading(false);

    if (error) {
      console.error('Error saving profile data:', error);
      Alert.alert('Error', 'Failed to save profile data.');
    } else {
      console.log('Profile data saved:', data);
      Alert.alert('Success', 'Profile data saved successfully!');
      // Update local state
      setUserData((prevData) => ({
        ...prevData,
        socialMedia: { instagram: instagramLink, facebook: facebookLink },
      }));
    }
  };
  
  // Add these state variables for photo selection and deletion
  const [selectedPhotos, setSelectedPhotos] = useState<string[]>([]);
  const [isSelectMode, setIsSelectMode] = useState(false);

  // Function to handle adding photos
  const handleAddPhotos = async () => {
    try {
      // Request permissions first
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Please allow access to your photo library to add photos.',
          [{ text: 'OK' }]
        );
        return;
      }
      
      // Launch image picker with consistent options
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        // Handle the selected image
        const newPhoto = result.assets[0].uri;
        
        // Ensure user is logged in
        if (!user?.id) {
          Alert.alert('Error', 'User not logged in.');
          return;
        }

        // Upload photo to Supabase storage
        const fileExt = newPhoto.split('.').pop();
        const fileName = `${user.id}/${Date.now()}.${fileExt}`;
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('avatars') // Assuming 'avatars' is your bucket name
          .upload(fileName, newPhoto, { cacheControl: '3600', upsert: false });

        if (uploadError) {
          console.error('Error uploading photo:', uploadError);
          Alert.alert('Error', 'Failed to upload photo.');
          return;
        }

        // Get public URL of the uploaded photo
        const { data: publicUrlData } = supabase.storage
          .from('avatars')
          .getPublicUrl(fileName);

        const publicUrl = publicUrlData.publicUrl;

        // Update user's profile in Supabase with the new photo URL
        const { error: updateError } = await supabase
          .from('profiles')
          .upsert(
            {
              id: user.id,
              photos: [...userData.photos, publicUrl],
            },
            { onConflict: 'id' }
          );

        if (updateError) {
          console.error('Error updating profile with photo URL:', updateError);
          Alert.alert('Error', 'Failed to update profile with photo URL.');
        } else {
          console.log('Profile updated successfully with new photo URL.');
          // Update local state after successful Supabase update
          const updatedPhotos = [...userData.photos, publicUrl];
          setUserData({
            ...userData,
            photos: updatedPhotos
          });
          setUserPhotos(updatedPhotos);
          Alert.alert('Success', 'Photo added and profile updated successfully!');
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    }
  };
  
  // Function to view all photos
  const handleViewPhotos = () => {
    setShowPhotosPage(true);
  };

  // Function to handle uploading discover card image
  const handleUploadDiscoverCardImage = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Please allow access to your photo library to upload images.',
          [{ text: 'OK' }]
        );
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const newImage = result.assets[0].uri;

        if (!user?.id) {
          Alert.alert('Error', 'User not logged in.');
          return;
        }

        const fileExt = newImage.split('.').pop();
        const fileName = `${user.id}/discover_card_${Date.now()}.${fileExt}`;
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('discover_cards') // Assuming a new bucket for discover cards
          .upload(fileName, newImage, { cacheControl: '3600', upsert: false });

        if (uploadError) {
          console.error('Error uploading discover card image:', uploadError);
          Alert.alert('Error', 'Failed to upload discover card image.');
          return;
        }

        const { data: publicUrlData } = supabase.storage
          .from('discover_cards')
          .getPublicUrl(fileName);

        const publicUrl = publicUrlData.publicUrl;

        const { error: updateError } = await supabase
          .from('profiles')
          .upsert(
            {
              id: user.id,
              discover_card_image: publicUrl, // Assuming a new column for discover card image
            },
            { onConflict: 'id' }
          );

        if (updateError) {
          console.error('Error updating profile with discover card image URL:', updateError);
          Alert.alert('Error', 'Failed to update profile with discover card image URL.');
        } else {
          console.log('Profile updated successfully with new discover card image URL.');
          setUserData((prevData) => ({
            ...prevData,
            discover_card_image: publicUrl,
          }));
          Alert.alert('Success', 'Discover card image uploaded and profile updated successfully!');
        }
      }
    } catch (error) {
      console.error('Error picking discover card image:', error);
      Alert.alert('Error', 'Failed to pick discover card image. Please try again.');
    }
  };
  
  // Add a function to close the photos page
  const closePhotosPage = () => {
    setShowPhotosPage(false);
  };

  // Add these functions for photo selection and deletion
  const toggleSelectMode = () => {
    setIsSelectMode(!isSelectMode);
    // Clear selections when exiting select mode
    if (isSelectMode) {
      setSelectedPhotos([]);
    }
  };

  const togglePhotoSelection = (photoUri: string) => {
    if (selectedPhotos.includes(photoUri)) {
      setSelectedPhotos(selectedPhotos.filter(uri => uri !== photoUri));
    } else {
      setSelectedPhotos([...selectedPhotos, photoUri]);
    }
  };

  const deleteSelectedPhotos = () => {
    if (selectedPhotos.length === 0) return;
    
    Alert.alert(
      'Delete Photos',
      `Are you sure you want to delete ${selectedPhotos.length} photo${selectedPhotos.length > 1 ? 's' : ''}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: () => {
            // Filter out the selected photos
            const updatedPhotos = userPhotos.filter(photo => !selectedPhotos.includes(photo));
            
            // Update state
            setUserPhotos(updatedPhotos);
            setUserData({
              ...userData,
              photos: updatedPhotos
            });
            
            // Exit select mode and clear selections
            setIsSelectMode(false);
            setSelectedPhotos([]);
            
            // In a real app, you would also update the backend here
            console.log('Deleted photos:', selectedPhotos);
          }
        }
      ]
    );
  };

  // Photos Page Container Component - Define it before using it
  const PhotosPageContainer = () => (
    <View style={styles.pageContainer}>
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.pageHeader}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={closePhotosPage}
          >
            <IconSymbol name="chevron.left" size={24} color="#333" />
          </TouchableOpacity>
          

          


          <Text style={styles.pageTitle}>My Photos</Text>
          <TouchableOpacity 
            style={styles.headerAction}
            onPress={toggleSelectMode}
          >
            <Text style={styles.headerActionText}>
              {isSelectMode ? 'Cancel' : 'Select'}
            </Text>
          </TouchableOpacity>
          

          


        </View>
        
        {isSelectMode && selectedPhotos.length > 0 && (
          <View style={styles.selectionActionBar}>
            <Text style={styles.selectedCountText}>
              {selectedPhotos.length} selected
            </Text>
            <TouchableOpacity 
              style={styles.deleteButton}
              onPress={deleteSelectedPhotos}
            >
              <IconSymbol name="trash" size={20} color="#FF4B00" />
              <Text style={styles.deleteButtonText}>Delete</Text>
            </TouchableOpacity>
          

          


          </View>
        )}
        
        <ScrollView style={styles.photosPageContent}>
          <View style={styles.photosGridContent}>
            {userPhotos.map((photo, index) => {
              console.log('Photo URI:', photo); // Add this line to log the photo URI
              return (
                <TouchableOpacity 
                  key={index}
                  style={[
                    styles.photoGridItem,
                    isSelectMode && selectedPhotos.includes(photo) && styles.selectedPhotoItem
                  ]}
                  onPress={() => isSelectMode ? togglePhotoSelection(photo) : null}
                  activeOpacity={isSelectMode ? 0.7 : 1}
                >
                  <Image source={{ uri: photo }} style={styles.photoGridImage} />
                  {isSelectMode && (
                    <View style={styles.selectionOverlay}>
                      {selectedPhotos.includes(photo) && (
                        <View style={styles.selectionCheckmark}>
                          <IconSymbol name="checkmark" size={16} color="#FFFFFF" />
                        </View>
                      )}
                    </View>
                  )}
                </TouchableOpacity>
          

          


              );
            })}
            
            {!isSelectMode && (
              <TouchableOpacity 
                style={styles.addPhotoButton}
                
              >
                <IconSymbol name="plus" size={30} color="#FF4B00" />
                <Text style={styles.addPhotoText}>Add Photo</Text>
              </TouchableOpacity>
          

          


            )}
          </View>
        </ScrollView>
      </SafeAreaView>
    </View>
  );

  // Function to handle UPI payment
  const handleBuyCoinsWithUPI = async (amount: number, coins: number, packageId: string) => {
    try {
      // Check if running in Expo Go
      const isExpoGo = Constants.appOwnership === 'expo';
      
      if (isExpoGo && Platform.OS === 'ios') {
        // Special handling for Expo Go on iOS
        Alert.alert(
          'Expo Go Limitation',
          'Direct UPI app linking is not supported in Expo Go on iOS. Would you like to use a web payment option instead?',
          [
            { text: 'Cancel', style: 'cancel' },
            { 
              text: 'Web Payment', 
              onPress: () => {
                // Simulate successful payment for testing in Expo Go
                Alert.alert(
                  'Development Mode',
                  'Would you like to simulate a successful payment? (This is only for testing)',
                  [
                    { text: 'Cancel', style: 'cancel' },
                    { 
                      text: 'Simulate Payment', 
                      onPress: async () => {
                        if (user?.id) {
                          const updated = await handleSuccessfulPayment(user.id, coins);
                          
                          if (updated) {
                            Alert.alert(
                              'Purchase Successful (Simulated)',
                              `You have purchased ${coins} Heat Coins!`
                            );
                            // Refresh user data to show updated coin balance
                            fetchUserData();
                          }
                        }
                      }
                    }
                  ]
                );
              }
            }
          ]
        );
        return;
      }
      
      // UPI payment options
      const upiOptions = [
        { id: 'phonepe', name: 'PhonePe', scheme: 'phonepe://' },
        { id: 'gpay', name: 'Google Pay', scheme: 'gpay://' },
        { id: 'paytm', name: 'Paytm', scheme: 'paytm://' },
        { id: 'bhim', name: 'BHIM UPI', scheme: 'bhim://' }
      ];
      
      // Check which UPI apps are installed
      const availableUPIApps = await Promise.all(
        upiOptions.map(async (app) => {
          try {
            const isAvailable = await Linking.canOpenURL(app.scheme);
            return { ...app, isAvailable };
          } catch (error) {
            console.log(`Error checking ${app.name} availability:`, error);
            return { ...app, isAvailable: false };
          }
        })
      );
      
      const installedApps = availableUPIApps.filter(app => app.isAvailable);
      
      if (installedApps.length === 0) {
        // No UPI apps installed or detected
        Alert.alert(
          'UPI Apps Not Detected',
          'We couldn\'t detect any UPI apps. This might be due to running in Expo Go or simulator. Would you like to simulate a payment for testing?',
          [
            { text: 'Cancel', style: 'cancel' },
            { 
              text: 'Simulate Payment', 
              onPress: async () => {
                if (user?.id) {
                  const updated = await handleSuccessfulPayment(user.id, coins);
                  
                  if (updated) {
                    Alert.alert(
                      'Purchase Successful (Simulated)',
                      `You have purchased ${coins} Heat Coins!`
                    );
                    // Refresh user data to show updated coin balance
                    fetchUserData();
                  }
                }
              }
            }
          ]
        );
        return;
      }
      
      // Show options for available UPI apps
      Alert.alert(
        'Choose Payment Method',
        'Select your preferred UPI payment app:',
        [
          ...installedApps.map(app => ({
            text: app.name,
            onPress: () => initiateUPIPayment(app.scheme, amount, coins, packageId)
          })),
          { text: 'Cancel', style: 'cancel' }
        ]
      );
    } catch (error) {
      console.error('Error in handleBuyCoinsWithUPI:', error);
      Alert.alert('Error', 'Something went wrong. Please try again later.');
    }
  };

  // Function to initiate UPI payment
  const initiateUPIPayment = async (appScheme: string, amount: number, coins: number, packageId: string) => {
    try {
      // Create a transaction ID
      const transactionId = `HEAT${Date.now()}`;
      
      // Create a UPI payment URI
      const upiParams = {
        pa: 'heatwaves@ybl', // Replace with your actual UPI ID
        pn: 'Heat Dating App',
        am: amount.toString(),
        tr: transactionId,
        tn: `Purchase ${coins} Heat Coins`
      };
      
      // Construct the UPI URI
      const upiUri = `${appScheme}pay?${Object.entries(upiParams)
        .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
        .join('&')}`;
      
      // Open the UPI app
      const supported = await Linking.canOpenURL(upiUri);
      
      if (supported) {
        await Linking.openURL(upiUri);
        
        // Since we can't automatically detect payment completion,
        // ask the user to confirm when payment is complete
        setTimeout(() => {
          Alert.alert(
            'Payment Status',
            'Did you complete the payment?',
            [
              { 
                text: 'No, Cancel', 
                style: 'cancel',
                onPress: () => console.log('Payment cancelled by user')
              },
              { 
                text: 'Yes, Completed', 
                onPress: async () => {
                  if (user?.id) {
                    // Verify the payment with the development server
                    const verified = await verifyUpiPayment(
                      user.id,
                      amount,
                      coins,
                      transactionId
                    );
                    
                    if (verified) {
                      const updated = await handleSuccessfulPayment(user.id, coins);
                      
                      if (updated) {
                        Alert.alert(
                          'Purchase Successful',
                          `You have purchased ${coins} Heat Coins!`,
                          [{ text: 'OK', onPress: () => setShowBuyCoinsModal(false) }]
                        );
                        // Refresh user data to show updated coin balance
                        fetchUserData();
                      } else {
                        Alert.alert(
                          'Error',
                          'We had trouble updating your coin balance. Please contact support.',
                          [{ text: 'OK' }]
                        );
                      }
                    } else {
                      Alert.alert(
                        'Payment Verification Failed',
                        'We could not verify your payment. If you completed the payment, please contact support.',
                        [{ text: 'OK' }]
                      );
                    }
                  }
                }
              }
            ]
          );
        }, 5000); // Give user 5 seconds to complete payment
      } else {
        Alert.alert('Error', 'Could not open the payment app. Please try another method.');
      }
    } catch (error) {
      console.error('Error initiating UPI payment:', error);
      Alert.alert('Error', 'Failed to initiate payment. Please try again.');
    }
  };

  // Add a function to fetch user data including coin balance
  const fetchUserData = async () => {
    if (user?.id) {
      try {
        setLoading(true);
        // Fetch user profile data
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();
        
        if (error) {
          console.error('Error fetching user data:', error);
        } else {
          // Update userData state with fetched data
          setUserData({
            ...userData,
            displayName: data.display_name || userData.displayName,
            email: data.email || userData.email,
            bio: data.bio || userData.bio,
            photos: data.photos || userData.photos,
            heatCoins: data.coin_balance || 0,
            preference: data.preference || 'Everyone'
          });
          // Set coin balance separately for components that need it
          setCoinBalance(data.coin_balance || 0);
          // Set auto-pay preference from the dedicated column if it exists
          setAutoPay(data.auto_pay_preference || false);
          // Set preference changed date
          setPreferenceChangedDate(data.preference_changed_date ? new Date(data.preference_changed_date) : null);
        }
      } catch (error) {
        console.error('Error in fetchUserData:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  // Call fetchUserData when component mounts
  useEffect(() => {
    fetchUserData();
  }, [user]);
  
  // Function to handle navigation to edit profile
  const handleEditProfile = () => {
    // Use the correct path format to navigate to the edit-profile screen in the (auth) directory
    router.push('/(auth)/edit-profile');
  };
  
  const handleSettings = () => {
    setShowSettingsModal(true);
  };
  
  const handleSavedProfiles = () => {
    router.push('/saved-profiles');
  };
  
  const handleBuyCoins = () => {
    setShowBuyCoinsModal(true);
  };
  
  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Logout', 
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
              router.replace('/login');
            } catch (error) {
              console.error('Logout error:', error);
              Alert.alert('Error', 'Failed to logout. Please try again.');
            }
          }
        }
      ]
    );
  };
  
  // Remove the handleChangePhoto function since we're not using it anymore
  // const handleChangePhoto = (direction: 'next' | 'prev') => {
  //   if (direction === 'next') {
  //     setPhotoIndex((photoIndex + 1) % userData.photos.length);
  //   } else {
  //     setPhotoIndex((photoIndex - 1 + userData.photos.length) % userData.photos.length);
  //   }
  // };

  // Function to handle coin purchase
  const handlePurchaseCoins = (amount: number) => {
    Alert.alert(
      'Purchase Successful',
      `You have purchased ${amount} Heat Coins!`,
      [{ text: 'OK', onPress: () => setShowBuyCoinsModal(false) }]
    );
    // In a real app, you would update the user's coin balance here
  };
  
  // Header animation - removed since we're not using the animated header anymore
  // const headerHeight = scrollY.interpolate({
  //   inputRange: [0, 100],
  //   outputRange: [0, -60],
  //   extrapolate: 'clamp',
  // });
  
  // const headerOpacity = scrollY.interpolate({
  //   inputRange: [0, 60, 90],
  //   outputRange: [1, 0.3, 0],
  //   extrapolate: 'clamp',
  // });
  
  // Settings Modal Component
  const SettingsModal = () => (
    <Modal
      visible={showSettingsModal}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowSettingsModal(false)}
    >
      <View style={styles.modalOverlay}>
        <BlurView intensity={50} style={styles.blurView} tint="dark">
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Settings</Text>
              <TouchableOpacity 
                style={styles.closeButton}
                onPress={() => setShowSettingsModal(false)}
              >
                <IconSymbol name="xmark" size={20} color="#333" />
              </TouchableOpacity>
          

          


            </View>
            
            <View style={styles.modalDivider} />
            
            <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
              <TouchableOpacity 
                style={styles.settingItem}
                onPress={() => {
                  setShowSettingsModal(false);
                  // Navigate to notifications page
                  router.push('/notifications');
                }}
              >
                <View style={styles.settingIconContainer}>
                  <IconSymbol name="bell.fill" size={18} color="#FFF" />
                </View>
                <Text style={styles.settingText}>Notifications</Text>
                <IconSymbol name="chevron.right" size={18} color="#999" style={styles.settingArrow} />
              </TouchableOpacity>
          

          


              
              <TouchableOpacity 
                style={styles.settingItem}
                onPress={() => {
                  setShowSettingsModal(false);
                  // Navigate to privacy page
                  router.push('/privacy');
                }}
              >
                <View style={styles.settingIconContainer}>
                  <IconSymbol name="lock.fill" size={18} color="#FFF" />
                </View>
                <Text style={styles.settingText}>Privacy</Text>
                <IconSymbol name="chevron.right" size={18} color="#999" style={styles.settingArrow} />
              </TouchableOpacity>
          

          


              
              <TouchableOpacity 
                style={styles.settingItem}
                onPress={() => {
                  setShowSettingsModal(false);
                  // Navigate to account page
                  router.push('/account');
                }}
              >
                <View style={styles.settingIconContainer}>
                  <IconSymbol name="person.fill" size={18} color="#FFF" />
                </View>
                <Text style={styles.settingText}>Account</Text>
                <IconSymbol name="chevron.right" size={18} color="#999" style={styles.settingArrow} />
              </TouchableOpacity>
          

          


              
              <TouchableOpacity 
                style={styles.settingItem}
                onPress={() => {
                  setShowSettingsModal(false);
                  // Navigate to terms page
                  router.push('/terms');
                }}
              >
                <View style={styles.settingIconContainer}>
                  <IconSymbol name="doc.text.fill" size={18} color="#FFF" />
                </View>
                <Text style={styles.settingText}>Terms & Policies</Text>
                <IconSymbol name="chevron.right" size={18} color="#999" style={styles.settingArrow} />
              </TouchableOpacity>
          

          


              
              <TouchableOpacity 
                style={[styles.settingItem, styles.dangerSettingItem]}
                onPress={handleLogout}
              >
                <View style={[styles.settingIconContainer, styles.dangerIconContainer]}>
                  <IconSymbol name="arrow.right.square.fill" size={18} color="#FFF" />
                </View>
                <Text style={[styles.settingText, styles.dangerSettingText]}>Logout</Text>
              </TouchableOpacity>
          

          


            </ScrollView>
          </View>
        </BlurView>
      </View>
    </Modal>
  );
  
  // Buy Coins Modal Component
  const BuyCoinsModal = () => (
    <Modal
      visible={showBuyCoinsModal}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowBuyCoinsModal(false)}
    >
      <View style={styles.modalOverlay}>
        <BlurView intensity={50} style={styles.blurView} tint="dark">
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Buy Heat Coins</Text>
              <TouchableOpacity 
                style={styles.closeButton}
                onPress={() => setShowBuyCoinsModal(false)}
              >
                <IconSymbol name="xmark" size={20} color="#FFF" />
              </TouchableOpacity>
          

          


            </View>
            
            <ScrollView style={styles.coinPackages} contentContainerStyle={styles.coinPackagesContent}>
              {coinPackages.map((pkg: CoinPackage) => (
                <TouchableOpacity 
                  key={pkg.id}
                  style={styles.coinPackage}
                  onPress={() => handleBuyCoinsWithUPI(pkg.discountedPrice || pkg.price, pkg.coins, pkg.id)}
                >
                  <View style={styles.coinPackageIcon}>
                    <IconSymbol name="flame.fill" size={24} color="#FFF" />
                    <Text style={styles.coinAmount}>{pkg.coins}</Text>
                  </View>
                  <View style={styles.coinPackageInfo}>
                    <Text style={styles.coinPackageTitle}>{pkg.name}</Text>
                    {pkg.discountedPrice ? (
                      <View style={styles.priceContainer}>
                        <Text style={styles.originalPrice}>₹{pkg.price}</Text>
                        <Text style={styles.coinPackagePrice}>₹{pkg.discountedPrice}</Text>
                      </View>
                    ) : (
                      <Text style={styles.coinPackagePrice}>₹{pkg.price}</Text>
                    )}
                  </View>
                  <TouchableOpacity 
                    style={styles.buyButton}
                    onPress={() => handleBuyCoinsWithUPI(pkg.discountedPrice || pkg.price, pkg.coins, pkg.id)}
                  >
                    <Text style={styles.buyButtonText}>Buy</Text>
                  </TouchableOpacity>
          

          


                </TouchableOpacity>
          

          


              ))}
            </ScrollView>
          </View>
        </BlurView>
      </View>
    </Modal>
  );

  // Photos Modal Component
  const PhotosModal = () => (
    <Modal
      visible={showPhotosModal}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowPhotosModal(false)}
    >
      <View style={styles.modalOverlay}>
        <BlurView intensity={50} style={styles.blurView} tint="dark">
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>My Photos</Text>
              <TouchableOpacity 
                style={styles.closeButton}
                onPress={() => setShowPhotosModal(false)}
              >
                <IconSymbol name="xmark" size={20} color="#FFF" />
              </TouchableOpacity>
          

          


            </View>
            
            <ScrollView style={styles.photosGrid}>
              <View style={styles.photosGridContent}>
                {userPhotos.map((photo, index) => (
                  <TouchableOpacity 
                    key={index}
                    style={styles.photoGridItem}
                  >
                    <Image source={{ uri: photo }} style={styles.photoGridImage} />
                  </TouchableOpacity>
          

          


                ))}
                
                <TouchableOpacity 
                  style={styles.addPhotoButton}
                  
                >
                  <IconSymbol name="plus" size={30} color="#FF4B00" />
                  <Text style={styles.addPhotoText}>Add Photo</Text>
                </TouchableOpacity>
          

          


              </View>
            </ScrollView>
          </View>
        </BlurView>
      </View>
    </Modal>
  );
  
  // Language Modal Component
  // Removed as it's no longer needed

  // Add this inside your component, before the return statement
  // This will check for updated coin balance when the screen receives focus
  useEffect(() => {
    // Check if there's an updated coin balance in the params
    if (params.updatedCoinBalance) {
      const newBalance = parseInt(params.updatedCoinBalance as string, 10);
      if (!isNaN(newBalance)) {
        // Update the user data with the new balance
        setUserData(prevData => ({
          ...prevData,
          heatCoins: newBalance
        }));
      }
    }
    
    // Set up a focus listener to refresh data when screen comes into focus
    const unsubscribe = navigation.addListener('focus', () => {
      // Fetch the latest data from the database
      fetchUserData();
    });
    
    return () => {
      // Clean up the listener when component unmounts
      unsubscribe();
    };
  }, [navigation, params.updatedCoinBalance]);

  // Function to update user preference
  const updatePreference = async (preference: string) => {
    if (user?.id) {
      try {
        // Update the local state first for immediate feedback
        setUserData(prev => ({
          ...prev,
          preference
        }));
        
        // Update the preference in the database
        const { error } = await supabase
          .from('profiles')
          .update({ preference })
          .eq('id', user.id);
        
        if (error) {
          console.error('Error updating preference:', error);
          // Revert to previous preference if there was an error
          setUserData(prev => ({
            ...prev,
            preference: prev.preference
          }));
          Alert.alert('Error', 'Failed to update preference. Please try again.');
        }
      } catch (error) {
        console.error('Error in updatePreference:', error);
      }
    }
  };

  // Function to handle preference change
  const handlePreferenceChange = async (preference: string) => {
    if (user?.id) {
      try {
        // If changing from 'Everyone' to something else, deduct 50 coins
        let newCoinBalance = userData.heatCoins;
        let newPreferenceChangedDate = preferenceChangedDate;
        
        if (userData.preference === 'Everyone' && preference !== 'Everyone') {
          // Check if user has enough coins
          if (userData.heatCoins < 50) {
            Alert.alert(
              "Insufficient Coins",
              "You need 50 coins to change your preference. Would you like to purchase more coins?",
              [
                { text: "Buy Coins", onPress: () => router.push('/purchase-coins') },
                { text: "Cancel", style: "cancel" }
              ]
            );
            return;
          }
          
          newCoinBalance = userData.heatCoins - 50;
          newPreferenceChangedDate = new Date();
        }
        
        // Update the local state first for immediate feedback
        setUserData(prev => ({
          ...prev,
          preference,
          heatCoins: newCoinBalance
        }));
        setCoinBalance(newCoinBalance);
        setPreferenceChangedDate(newPreferenceChangedDate);
        
        // First, try to update with all fields
        let updateResult = await supabase
          .from('profiles')
          .update({ 
            preference,
            coin_balance: newCoinBalance,
            preference_changed_date: newPreferenceChangedDate
          })
          .eq('id', user.id);
        
        // If we get a column not found error, try updating without the preference_changed_date
        if (updateResult.error && updateResult.error.code === 'PGRST204') {
          console.log('Column preference_changed_date not found, updating without it');
          
          // Try updating just the preference and coin balance
          updateResult = await supabase
            .from('profiles')
            .update({ 
              preference,
              coin_balance: newCoinBalance
            })
            .eq('id', user.id);
          
          // Show an alert about the missing column
          Alert.alert(
            'Database Update Required',
            'Please run the database migration to enable all features.',
            [{ text: 'OK' }]
          );
        }
        
        if (updateResult.error) {
          console.error('Error updating preference:', updateResult.error);
          // Revert to previous preference if there was an error
          setUserData(prev => ({
            ...prev,
            preference: prev.preference,
            heatCoins: userData.heatCoins
          }));
          setCoinBalance(userData.heatCoins);
          setPreferenceChangedDate(preferenceChangedDate);
          Alert.alert('Error', 'Failed to update preference. Please try again.');
        } else {
          if (userData.preference === 'Everyone' && preference !== 'Everyone') {
            Alert.alert(
              'Preference Updated',
              `Your preference has been updated to "${preference}". 50 coins have been deducted from your balance. This will reset to "Everyone" after 30 days${autoPay ? ' unless auto-pay is active' : ''}.`
            );
          } else {
            Alert.alert(
              'Preference Updated',
              `Your preference has been updated to "${preference}".`
            );
          }
        }
      } catch (error) {
        console.error('Error in handlePreferenceChange:', error);
      }
    }
  };

  // Function to toggle auto-pay
  const toggleAutoPay = async () => {
    if (user?.id) {
      try {
        const newAutoPayValue = !autoPay;
        
        // Update local state first
        setAutoPay(newAutoPayValue);
        
        // Instead of using metadata column, let's use a dedicated column
        // First, check if the column exists in the database
        const { error } = await supabase
          .from('profiles')
          .update({ auto_pay_preference: newAutoPayValue })
          .eq('id', user.id);
        
        if (error) {
          console.error('Error updating auto-pay preference:', error);
          // Revert if error
          setAutoPay(autoPay);
          
          // If the column doesn't exist, let's inform the user
          if (error.code === 'PGRST204') {
            Alert.alert(
              'Database Update Required',
              'The auto-pay feature requires a database update. Please contact support.',
              [{ text: 'OK' }]
            );
          } else {
            Alert.alert('Error', 'Failed to update auto-pay preference. Please try again.');
          }
        } else {
          Alert.alert(
            'Auto-Pay Updated',
            newAutoPayValue 
              ? 'Auto-pay is now ON. Your preference will be automatically renewed every 30 days if you have enough coins.'
              : 'Auto-pay is now OFF. Your preference will reset to "Everyone" after 30 days.'
          );
        }
      } catch (error) {
        console.error('Error in toggleAutoPay:', error);
      }
    }
  };

  // Debug and fix the auto-pay toggle for Android
  useEffect(() => {
    console.log("Platform:", Platform.OS);
    console.log("Preference:", userData.preference);
    console.log("Should show auto-pay:", userData.preference !== 'Everyone');
  }, [userData.preference]);

  // Check if preference needs to be reset after 30 days
  useEffect(() => {
    if (user?.id && preferenceChangedDate && userData.preference !== 'Everyone') {
      const now = new Date();
      const thirtyDaysInMs = 30 * 24 * 60 * 60 * 1000;
      const timeSinceChange = now.getTime() - preferenceChangedDate.getTime();
      
      // If 30 days have passed
      if (timeSinceChange >= thirtyDaysInMs) {
        // If auto-pay is on and user has enough coins, deduct coins and update date
        if (autoPay && userData.heatCoins >= 50) {
          const newCoinBalance = userData.heatCoins - 50;
          const newPreferenceChangedDate = new Date();
          
          // Update in database
          supabase
            .from('profiles')
            .update({ 
              coin_balance: newCoinBalance,
              preference_changed_date: newPreferenceChangedDate
            })
            .eq('id', user.id)
            .then(({ error }: { error: any }) => {
              if (error) {
                console.error('Error in auto-pay renewal:', error);
                // If error, reset to Everyone
                handlePreferenceChange('Everyone');
              } else {
                // Update local state
                setUserData(prev => ({
                  ...prev,
                  heatCoins: newCoinBalance
                }));
                setCoinBalance(newCoinBalance);
                setPreferenceChangedDate(newPreferenceChangedDate);
                
                // Notify user
                Alert.alert(
                  'Preference Renewed',
                  `Your preference has been automatically renewed for another 30 days. 50 coins have been deducted from your balance.`
                );
              }
            });
        } else {
          // If auto-pay is off or not enough coins, reset to Everyone
          handlePreferenceChange('Everyone');
        }
      }
    }
  }, [user, preferenceChangedDate, userData.preference, autoPay, userData.heatCoins]);

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar barStyle="dark-content" />
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Profile</Text>
        <TouchableOpacity onPress={handleSettings} style={styles.settingsButton}>
          <IconSymbol name="gear" size={22} color="#333" />
        </TouchableOpacity>
      </View>
      
      {/* Main content */}
      <Animated.ScrollView 
        style={styles.scrollView} 
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: true }
        )}
        scrollEventThrottle={16}
      >
        {/* User Header with Avatar and Heat Coins */}
        <View style={styles.userHeaderContainer}>
          {/* Avatar */}
          <TouchableOpacity onPress={() => router.push(`/profile-details/${user?.id}`)} style={styles.avatarContainer}>
            <Image
              source={userData.photos && userData.photos.length > 0 ? { uri: userData.photos[0] } : (user?.photoURL ? { uri: user.photoURL } : undefined)}
              style={styles.avatarImage}
            />
          </TouchableOpacity>
          
          {/* User Info and Coins */}
          <View style={styles.userInfoWrapper}>
            {/* User Info */}
            <View style={styles.userInfoSection}>
              <Text style={styles.userName}>{userData.displayName}</Text>
              <Text style={styles.userEmail}>{userData.email}</Text>
            </View>
            
            {/* Heat Coins */}
            <View style={styles.coinContainer}>
              <LinearGradient
                colors={['#FF4B00', '#FF6B00']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.coinGradient}
              >
                <IconSymbol name="flame.fill" size={28} color="#FFF" />
                <Text style={styles.coinText}>{userData.heatCoins}</Text>
              </LinearGradient>
              <TouchableOpacity 
                style={styles.buyCoinsButton}
                onPress={() => router.push('/purchase-coins')}
              >
                <Text style={styles.buyCoinsButtonText}>Buy</Text>
              </TouchableOpacity>
          

          


            </View>
          </View>
        </View>
        
        {/* Action Buttons */}
        <View style={styles.actionButtonsContainer}>
          <TouchableOpacity 
            style={styles.primaryActionButton}
            onPress={handleEditProfile}
          >
            <LinearGradient
              colors={['#FF4B00', '#FF6B00']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.primaryButtonGradient}
            >
              <Text style={styles.primaryActionButtonText}>Edit Profile</Text>
            </LinearGradient>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.secondaryActionButton}
            onPress={() => router.push('/discover-card')}
          >
            <Text style={styles.secondaryActionButtonText}>Upload Discover Card Image</Text>
          </TouchableOpacity>

        </View>
        

        
        {/* Profile Info */}
        <View style={styles.profileInfo}>
          {/* About */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>About</Text>
            <Text style={styles.bioText}>{userData.bio}</Text>
          </View>
          
          {/* Basic Info */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Basic Info</Text>
            
            <View style={styles.infoItem}>
              <IconSymbol name="briefcase.fill" size={20} color="#666" />
              <Text style={styles.infoText}>{userData.occupation}</Text>
            </View>
            
            <View style={styles.infoItem}>
              <IconSymbol name="book.fill" size={20} color="#666" />
              <Text style={styles.infoText}>{userData.education}</Text>
            </View>
            
            {/* Interested In */}
            <View style={styles.preferenceContainer}>
              <View style={styles.preferenceHeader}>
                <Text style={styles.preferenceTitle}>Interested In</Text>
                
                {/* Auto-pay toggle in the same line as the title */}
                {userData.preference !== 'Everyone' && (
                  <View style={styles.autoPayContainer}>
                    <Text style={styles.autoPayLabel}>Auto-pay</Text>
                    <Switch
                      value={autoPay}
                      onValueChange={toggleAutoPay}
                      trackColor={{ false: '#767577', true: '#FF4B00' }}
                      thumbColor={autoPay ? '#fff' : '#f4f3f4'}
                    />
                  </View>
                )}
              </View>
              
              {/* Preference options */}
              <View style={styles.preferenceOptions}>
                {['Men', 'Women', 'Everyone'].map((option) => (
                  <TouchableOpacity 
                    key={option}
                    style={[
                      styles.preferenceOption,
                      userData.preference === option && styles.selectedPreference
                    ]}
                    onPress={() => handlePreferenceChange(option)}
                  >
                    <Text style={[
                      styles.preferenceOptionText,
                      userData.preference === option && styles.selectedPreferenceText
                    ]}>
                      {option}
                    </Text>
                  </TouchableOpacity>
          

          

          


                ))}
              </View>
              
              {/* Preference activation period */}
              {userData.preference !== 'Everyone' && (
                <View style={styles.preferenceActivationContainer}>
                  <View style={styles.dateContainer}>
                    <Text style={styles.dateLabel}>Activated:</Text>
                    <Text style={styles.dateValue}>
                      {preferenceChangedDate 
                        ? preferenceChangedDate.toLocaleDateString() 
                        : new Date().toLocaleDateString()}
                    </Text>
                  </View>
                  
                  <View style={styles.dateContainer}>
                    <Text style={styles.dateLabel}>Expires:</Text>
                    <Text style={styles.dateValue}>
                      {preferenceChangedDate 
                        ? new Date(
                            preferenceChangedDate.getTime() + 30 * 24 * 60 * 60 * 1000
                          ).toLocaleDateString()
                        : new Date(
                            new Date().getTime() + 30 * 24 * 60 * 60 * 1000
                          ).toLocaleDateString()}
                    </Text>
                  </View>
                  
                  {/* Days remaining indicator */}
                  <View style={styles.daysRemainingContainer}>
                    {(() => {
                      const now = new Date();
                      const startDate = preferenceChangedDate || now;
                      const expiryDate = new Date(
                        startDate.getTime() + 30 * 24 * 60 * 60 * 1000
                      );
                      const daysRemaining = Math.max(
                        0, 
                        Math.ceil((expiryDate.getTime() - now.getTime()) / (24 * 60 * 60 * 1000))
                      );
                      
                      return (
                        <>
                          <Text style={styles.daysRemainingLabel}>
                            {daysRemaining} {daysRemaining === 1 ? 'day' : 'days'} remaining
                          </Text>
                          <View style={styles.progressBarContainer}>
                            <View 
                              style={[
                                styles.progressBar, 
                                { width: `${Math.min(100, (daysRemaining / 30) * 100)}%` }
                              ]} 
                            />
                          </View>
                        </>
                      );
                    })()}
                  </View>
                </View>
              )}
              
              {userData.preference !== 'Everyone' && (
                <View style={styles.expiryContainer}>
                  <Text style={styles.expiryNote}>
                    {autoPay 
                      ? 'Your preference will auto-renew every 30 days for 50 coins' 
                      : 'Your preference will reset to Everyone after 30 days'
                    }
                  </Text>
                </View>
              )}
              
              <Text style={styles.preferenceNote}>
                {userData.preference === 'Everyone' 
                  ? "Specific preferences cost 50 coins for 30 days" 
                  : ""}
              </Text>
            </View>
          </View>
          
          {/* Social Media */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Social Media</Text>
            
            {/* Social Media Input */}
            <View style={styles.socialInputContainer}>
              <View style={styles.socialMediaRow}>
              <FontAwesome name="instagram" size={24} color="#E1306C" style={styles.socialIcon} />
              <TextInput
                style={styles.socialInput}
                placeholder="Instagram Profile Link"
                placeholderTextColor="#999"
                value={instagramLink}
                onChangeText={setInstagramLink}
              />
              {instagramLink ? (
                <TouchableOpacity onPress={() => Linking.openURL(instagramLink)} style={styles.socialMediaIcon}>
                  <FontAwesome name="instagram" size={24} color="#E4405F" />
                </TouchableOpacity>
          

          


              ) : null}
            </View>
            <View style={styles.socialMediaRow}>
              <FontAwesome name="facebook" size={24} color="#4267B2" style={styles.socialIcon} />
              <TextInput
                style={styles.socialInput}
                placeholder="Facebook Profile Link"
                placeholderTextColor="#999"
                value={facebookLink}
                onChangeText={setFacebookLink}
              />
              {facebookLink ? (
                <TouchableOpacity onPress={() => Linking.openURL(facebookLink)} style={styles.socialMediaIcon}>
                  <FontAwesome name="facebook" size={24} color="#3b5998" />
                </TouchableOpacity>
          

          


              ) : null}
            </View>
            <TouchableOpacity style={styles.saveSocialButton} onPress={handleSaveSocialMedia}>
              <Text style={styles.saveSocialButtonText}>Save Social Media</Text>
            </TouchableOpacity>
          

          


            </View>


          </View>
          
          {/* Interests */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Interests</Text>
            <View style={styles.interestsContainer}>
              {userData.interests.map((interest, index) => (
                <View key={index} style={styles.interestBadge}>
                  <Text style={styles.interestText}>{interest}</Text>
                </View>
              ))}
            </View>
          </View>
          
          {/* Actions */}
          <View style={styles.actionsContainer}>
            <TouchableOpacity 
              style={styles.actionButton}
              onPress={handleSavedProfiles}
            >
              <IconSymbol name="heart.fill" size={24} color="#FF3B5C" />
              <Text style={styles.actionText}>Saved Profiles</Text>
            </TouchableOpacity>
          

          


          </View>
        </View>
        {/* Add padding at the bottom to ensure logout button is visible */}
        <View style={styles.bottomPadding} />
      </Animated.ScrollView>
      
      {/* Render Modals */}
      <SettingsModal />
      <BuyCoinsModal />
      <PhotosModal />
      {/* LanguageModal is removed */}
      
      {/* Conditionally render the Photos Page Container */}
      {showPhotosPage && <PhotosPageContainer />}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingBottom: 100, // Use same padding for both platforms
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15, // Use same padding for both platforms
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  settingsButton: {
    padding: 8,
  },
  userHeaderContainer: {
    padding: 20,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  userPhotoContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  userPhoto: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#F0F0F0',
  },
  editPhotoButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#FF4B00',
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  userInfoWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userInfoSection: {
    flex: 1,
  },
  userName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    color: '#666',
  },
  coinContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  coinGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  coinText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 6,
  },
  buyCoinsButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginLeft: 8,
    borderWidth: 1,
    borderColor: '#FF4B00',
  },
  buyCoinsButtonText: {
    color: '#FF4B00',
    fontWeight: 'bold',
    fontSize: 14,
  },
  viewPhotosButton: {
    marginHorizontal: 20,
    marginTop: 20,
    marginBottom: 10,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 10,
  },
  buttonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 8,
  },
  profileInfo: {
    padding: 20,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  bioText: {
    fontSize: 16,
    color: '#666',
    lineHeight: 22,
  },
  preferenceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  preferenceTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  preferenceOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  preferenceOption: {
    flex: 1,
    padding: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#DDDDDD',
    marginHorizontal: 4,
    alignItems: 'center',
  },
  selectedPreference: {
    borderColor: '#FF4B00',
    backgroundColor: 'rgba(255, 75, 0, 0.1)',
  },
  disabledOption: {
    opacity: 0.7,
  },
  preferenceText: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  selectedPreferenceText: {
    color: '#FF4B00',
    fontWeight: 'bold',
  },
  autoPayContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 'auto',
    backgroundColor: '#fff',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  autoPayLabel: {
    fontSize: 14,
    color: '#666',
    marginRight: 8,
    fontWeight: '500',
  },
  expiryContainer: {
    marginTop: 8,
    paddingHorizontal: 16,
  },
  expiryNote: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
  },



  socialInputContainer: {
    marginBottom: 20,
  },
  socialMediaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  socialIcon: {
    width: 24,
    height: 24,
    marginRight: 10,
  },
  socialInput: {
    flex: 1,
    height: 40,
    backgroundColor: '#F0F0F0',
    borderRadius: 8,
    paddingHorizontal: 10,
    color: '#333',
    fontSize: 16,
  },
  saveSocialButton: {
    backgroundColor: '#FF4B00',
    borderRadius: 5,
    paddingVertical: 10,
    paddingHorizontal: 15,
    alignItems: 'center',
    marginTop: 10,
  },
  saveSocialButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  socialMediaIcon: {
    marginLeft: 10,
  },
  interestsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  interestBadge: {
    backgroundColor: '#F0F0F0',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
  },
  interestText: {
    color: '#333',
    fontSize: 14,
  },

  actionsContainer: {
    marginTop: 10,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
    padding: 12,
    borderRadius: 8,
  },
  actionText: {
    fontSize: 16,
    color: '#333',
    marginLeft: 12,
  },
  bottomPadding: {
    height: 20,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  blurView: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '90%',
    maxHeight: '80%',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  closeButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F0F0F0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalDivider: {
    height: 1,
    backgroundColor: '#F0F0F0',
    marginHorizontal: 0,
  },
  modalContent: {
    paddingVertical: 8,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  settingIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FF4B00',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  settingText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  settingArrow: {
    marginLeft: 8,
  },
  dangerSettingItem: {
    marginTop: 8,
    borderBottomWidth: 0,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  dangerIconContainer: {
    backgroundColor: '#FF3B30',
  },
  dangerSettingText: {
    color: '#FF3B30',
  },
  // Add missing coin package styles
  coinPackages: {
    width: '100%',
    maxHeight: '80%',
  },
  coinPackagesContent: {
    padding: 16,
  },
  coinPackage: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  coinPackageIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#FF4B00',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  coinAmount: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
  },
  coinPackageInfo: {
    flex: 1,
  },
  coinPackageTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  originalPrice: {
    fontSize: 14,
    color: '#999',
    textDecorationLine: 'line-through',
    marginRight: 8,
  },
  coinPackagePrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  buyButton: {
    backgroundColor: '#FF4B00',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buyButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 14,
  },
  // Add missing avatar styles
  avatarContainer: {
    width: width * 0.8, // Adjust width to fit screen better
    height: width * 0.8, // Make it square
    borderRadius: 20, // Slightly rounded corners for the container
    alignSelf: 'center',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: 'hidden', // Clip content to rounded corners
  },
  avatarImage: {
    width: width * 0.8, // Each image takes full width of the container
    height: width * 0.8, // Each image takes full height of the container
    borderRadius: 20, // Apply border radius to images as well
    borderWidth: 3,
    borderColor: Colors.primary,
  },
  // Add missing action button styles
  actionButtonsContainer: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginTop: 20,
    marginBottom: 16,
  },
  primaryActionButton: {
    width: '100%',
    marginBottom: 10,
    borderRadius: 10,
    overflow: 'hidden',
  },
  primaryButtonGradient: {
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  primaryActionButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
  },
  secondaryActionButton: {
    width: '100%',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#FF4B00',
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  secondaryActionButtonText: {
    color: '#FF4B00',
    fontWeight: 'bold',
    fontSize: 16,
  },
  // Add missing info item styles
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoText: {
    fontSize: 16,
    color: '#666',
    marginLeft: 12,
  },
  // Add missing preference styles
  preferenceContainer: {
    marginTop: 16,
  },
  preferenceOptionText: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  coinBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FF4B00',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    marginTop: 4,
  },
  coinBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 2,
  },
  freeBadge: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    marginTop: 4,
  },
  freeBadgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  preferenceNote: {
    fontSize: 12,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
  },
  // Add styles for the Android auto-pay button
  androidAutoPayButton: {
    marginTop: 16,
    marginBottom: 8,
    borderRadius: 8,
    overflow: 'hidden',
    marginHorizontal: 16,
  },
  autoPayButtonGradient: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  autoPayButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  autoPayDescription: {
    fontSize: 12,
    color: '#FFFFFF',
    textAlign: 'center',
    marginTop: 4,
    opacity: 0.8,
  },
  // Photos grid styles
  photosGrid: {
    flex: 1,
    paddingHorizontal: 15,
    paddingTop: 15,
  },
  photosGridContent: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingBottom: 20,
  },
  photoGridItem: {
    width: '30%',
    aspectRatio: 1,
    marginBottom: 15,
    borderRadius: 12,
    overflow: 'hidden',
  },
  photoGridImage: {
    width: '100%',
    height: '100%',
  },
  addPhotoButton: {
    width: '30%',
    aspectRatio: 1,
    marginBottom: 15,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#FF4B00',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 75, 0, 0.05)',
  },
  addPhotoText: {
    marginTop: 8,
    fontSize: 14,
    fontWeight: '500',
    color: '#FF4B00',
  },
  // Add these styles for the auto-pay button
  autoPayButton: {
    marginTop: 16,
    marginHorizontal: 16,
    borderRadius: 8,
    overflow: 'hidden',
  },
  // Add styles for the preference activation period display
  preferenceActivationContainer: {
    marginTop: 16,
    marginBottom: 8,
    padding: 12,
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#FF4B00',
  },
  dateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  dateLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666',
  },
  dateValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  daysRemainingContainer: {
    marginTop: 8,
  },
  daysRemainingLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FF4B00',
    marginBottom: 4,
    textAlign: 'center',
  },
  progressBarContainer: {
    height: 6,
    backgroundColor: '#E0E0E0',
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: '#FF4B00',
    borderRadius: 3,
  },
  // Photos Page Container Component
  pageContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#FFFFFF',
    zIndex: 1000,
  },
  safeArea: {
    flex: 1,
  },
  pageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  pageTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333333',
  },
  headerSpacer: {
    width: 40,
  },
  photosPageContent: {
    flex: 1,
    padding: 16,
  },
  // Remove all duplicate styles - don't add photosGridContent, photoGridItem, 
  // photoGridImage, addPhotoButton, or addPhotoText again
  headerAction: {
    paddingHorizontal: 12,
  },
  headerActionText: {
    fontSize: 16,
    color: '#FF4B00',
    fontWeight: '500',
  },
  selectionActionBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 10,
    backgroundColor: '#F8F8F8',
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  selectedCountText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    backgroundColor: 'rgba(255, 75, 0, 0.1)',
    borderRadius: 8,
  },
  deleteButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#FF4B00',
    marginLeft: 6,
  },
  selectionOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectionCheckmark: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#FF4B00',
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedPhotoItem: {
    borderWidth: 3,
    borderColor: '#FF4B00',
  },
});

























































