// Minimal video call service

export const startVideoCall = async (userId: string) => {
  console.log(`Starting video call with ${userId}`);
  return true;
};

export const useVideoCall = (contactId: string, contactName: string, contactAvatar: string) => {
  // Update callState to be a string enum value instead of an object
  const callState = "connecting" as "connecting" | "ringing" | "ongoing" | "ended";

  // Define stream types with toURL method
  interface Stream {
    toURL: () => string;
  }

  // Create mock streams with toURL method
  const localStream: Stream | null = {
    toURL: () => "mock://local-stream-url"
  };
  
  const remoteStream: Stream | null = {
    toURL: () => "mock://remote-stream-url"
  };

  const makeCall = async () => {
    console.log(`Making video call to ${contactId}`);
    return true;
  };

  const acceptCall = () => {
    console.log(`Accepting video call from ${contactId}`);
  };

  const endCall = () => {
    console.log(`Ending video call with ${contactId}`);
  };

  const toggleMute = () => {
    console.log(`Toggling mute for video call with ${contactId}`);
  };

  const toggleVideo = () => {
    console.log(`Toggling video for call with ${contactId}`);
  };

  const switchCamera = () => {
    console.log(`Switching camera for call with ${contactId}`);
  };

  const formatDuration = () => {
    return '00:00';
  };

  return {
    callState,
    localStream,
    remoteStream,
    makeCall,
    acceptCall,
    endCall,
    toggleMute,
    toggleVideo,
    switchCamera,
    formatDuration
  };
};


