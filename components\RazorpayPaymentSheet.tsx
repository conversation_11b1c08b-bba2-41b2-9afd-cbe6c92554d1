import { useState } from 'react';
import { ActivityIndicator, Alert, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Colors } from '../constants/Colors';
import { CoinPackage, coinPackages } from '../services/paymentService';

// Mock implementation for Expo Go
const mockRazorpayCheckout = {
  open: (options: any) => {
    return new Promise((resolve, reject) => {
      // Simulate a successful payment after 1 second
      setTimeout(() => {
        resolve({
          razorpay_payment_id: 'mock_payment_id_' + Date.now(),
          razorpay_order_id: options.order_id,
          razorpay_signature: 'mock_signature_' + Date.now()
        });
      }, 1000);
    });
  }
};

interface RazorpayPaymentSheetProps {
  onSuccess: (coins: number) => void;
  onCancel: () => void;
  selectedPackage: string;
}

export default function RazorpayPaymentSheet({ 
  onSuccess, 
  onCancel,
  selectedPackage 
}: RazorpayPaymentSheetProps) {
  const [loading, setLoading] = useState(false);
  
  // Find the selected package
  const packageDetails = coinPackages.find((pkg: CoinPackage) => pkg.id === selectedPackage);
  
  const handlePayment = async () => {
    if (!packageDetails) {
      Alert.alert('Error', 'Invalid package selected');
      onCancel();
      return;
    }
    
    try {
      setLoading(true);
      
      // Mock order creation
      const order = {
        id: 'order_' + Date.now(),
        amount: packageDetails.price * 100
      };
      
      const options = {
        description: `${packageDetails.coins} Heat Coins`,
        image: 'https://your-app-logo-url.png',
        currency: packageDetails.currency,
        key: 'rzp_test_mock_key',
        amount: order.amount,
        name: 'Heatwaves App',
        order_id: order.id,
        prefill: {
          email: '<EMAIL>',
          contact: '9999999999',
          name: 'Heatwaves User'
        },
        theme: { color: Colors.primary }
      };
      
      // Use mock implementation
      mockRazorpayCheckout.open(options)
        .then((data: any) => {
          // Simulate successful payment verification
          Alert.alert(
            'Payment Successful',
            `You've purchased ${packageDetails.coins} Heat Coins!`,
            [{ text: 'OK', onPress: () => onSuccess(packageDetails.coins) }]
          );
        })
        .catch((error: any) => {
          Alert.alert('Error', `Payment failed: ${error}`);
          onCancel();
        });
    } catch (error) {
      console.error('Error setting up payment:', error);
      Alert.alert('Error', 'Failed to set up payment. Please try again.');
      onCancel();
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <View style={styles.container}>
      {packageDetails && (
        <>
          <Text style={styles.title}>Purchase Coins</Text>
          <View style={styles.packageDetails}>
            <Text style={styles.packageName}>{packageDetails.id.charAt(0).toUpperCase() + packageDetails.id.slice(1)} Pack</Text>
            <Text style={styles.packageCoins}>{packageDetails.coins} Coins</Text>
            <Text style={styles.packagePrice}>₹{packageDetails.price}</Text>
          </View>
          
          <TouchableOpacity 
            style={styles.payButton}
            onPress={handlePayment}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color="#FFFFFF" />
            ) : (
              <Text style={styles.payButtonText}>Pay Now</Text>
            )}
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.cancelButton}
            onPress={onCancel}
            disabled={loading}
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        </>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    width: '90%',
    maxWidth: 400,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  packageDetails: {
    alignItems: 'center',
    marginBottom: 20,
  },
  packageName: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  packageCoins: {
    fontSize: 24,
    color: Colors.primary,
    marginVertical: 10,
  },
  packagePrice: {
    fontSize: 18,
  },
  payButton: {
    backgroundColor: Colors.primary,
    borderRadius: 8,
    padding: 15,
    alignItems: 'center',
    marginBottom: 10,
  },
  payButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  cancelButton: {
    padding: 15,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: Colors.primary,
    fontSize: 16,
  },
});


