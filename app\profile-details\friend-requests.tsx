import { useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  FlatList,
  Image,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { IconSymbol } from '../../components/ui/IconSymbol';
import { useAuth } from '../../context/AuthContext';
import { addParticipant, createConversation } from '../../services/chatService';
import { FriendNotification, getNotifications, markAllAsRead } from '../../services/friendNotification';

export default function FriendRequestsScreen() {
  const [notifications, setNotifications] = useState<FriendNotification[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const auth = useAuth();
  
  // Load notifications
  useEffect(() => {
    const loadNotifications = async () => {
      try {
        setLoading(true);
        // Use id instead of uid
        const data = await getNotifications(auth?.user?.id || 'demo-user');
        setNotifications(data);
        
        // Mark all notifications as read when the user visits this screen
        await markAllAsRead(auth?.user?.id || 'demo-user');
      } catch (error) {
        console.error('Error loading notifications:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadNotifications();
  }, [auth?.user?.id]); // Change dependency to id

  const formatTimestamp = (timestamp: any) => {
    // Simple timestamp formatting
    if (!timestamp) return 'Just now';
    
    const now = new Date();
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    const diffDays = Math.floor(diffHours / 24);
    if (diffDays < 7) return `${diffDays}d ago`;
    
    return date.toLocaleDateString();
  };

  const handleAcceptRequest = async (notification: FriendNotification) => {
    try {
      // Accept friend request logic
      Alert.alert('Success', `You are now friends with ${notification.senderName}`);
      
      // Create a conversation
      const conversationId = await createConversation(
        auth?.user?.id || 'demo-user', // Change uid to id
        notification.userId,
        `${auth?.user?.displayName || 'You'} and ${notification.senderName} are now connected!`
      );
      
      // Add participants
      await addParticipant({
        id: auth?.user?.id || 'demo-user', // Change uid to id
        displayName: auth?.user?.displayName || 'You',
        photoURL: auth?.user?.photoURL || '',
        isOnline: true,
        lastSeen: Date.now()
      });

      await addParticipant({
        id: notification.userId,
        displayName: notification.senderName,
        photoURL: notification.senderPhoto || '',
        isOnline: false,
        lastSeen: Date.now()
      });
      
      // Remove the notification from the list
      setNotifications(notifications.filter(n => n.id !== notification.id));
      
      // Navigate to the conversation
      router.push({
        pathname: '/conversation/[id]',
        params: { id: conversationId.toString() } // Convert to string
      });
    } catch (error) {
      console.error('Error accepting friend request:', error);
      Alert.alert('Error', 'Failed to accept friend request. Please try again.');
    }
  };

  const handleDeclineRequest = (notification: FriendNotification) => {
    // Decline friend request logic
    Alert.alert(
      'Decline Request',
      `Are you sure you want to decline ${notification.senderName}'s friend request?`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Decline',
          style: 'destructive',
          onPress: () => {
            // Remove the notification from the list
            setNotifications(notifications.filter(n => n.id !== notification.id));
            Alert.alert('Success', 'Friend request declined');
          },
        },
      ]
    );
  };

  const renderNotification = ({ item }: { item: FriendNotification }) => {
    return (
      <View style={styles.notificationItem}>
        <Image 
          source={{ uri: item.senderPhoto || 'https://randomuser.me/api/portraits/lego/1.jpg' }} 
          style={styles.avatar} 
        />
        
        <View style={styles.notificationContent}>
          <Text style={styles.senderName}>{item.senderName}</Text>
          
          <Text style={styles.notificationText}>
            {item.type === 'request' && 'sent you a friend request'}
            {item.type === 'accepted' && 'accepted your friend request'}
            {item.type === 'message' && 'sent you a message'}
          </Text>
          
          <Text style={styles.timestamp}>
            {formatTimestamp(item.timestamp)}
          </Text>
        </View>
        
        {item.type === 'request' && (
          <View style={styles.actionButtons}>
            <TouchableOpacity 
              style={[styles.actionButton, styles.acceptButton]}
              onPress={() => handleAcceptRequest(item)}
            >
              <Text style={styles.actionButtonText}>Accept</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.actionButton, styles.declineButton]}
              onPress={() => handleDeclineRequest(item)}
            >
              <Text style={[styles.actionButtonText, styles.declineButtonText]}>Decline</Text>
            </TouchableOpacity>
          </View>
        )}
        
        {item.type === 'accepted' && (
          <TouchableOpacity 
            style={[styles.actionButton, styles.messageButton]}
            onPress={() => {
              router.push({
                pathname: '/conversation/[id]',
                params: { id: item.userId || 'new' } // Change conversationId to userId or another property that exists
              });
            }}
          >
            <Text style={styles.actionButtonText}>Message</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <IconSymbol name="chevron.left" size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Friend Requests</Text>
        <View style={styles.headerRight} />
      </View>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#FF5722" />
        </View>
      ) : notifications.length === 0 ? (
        <View style={styles.emptyContainer}>
          <IconSymbol name="person.badge.plus" size={48} color="#ccc" />
          <Text style={styles.emptyText}>No friend requests</Text>
          <Text style={styles.emptySubtext}>
            When someone sends you a friend request, it will appear here
          </Text>
        </View>
      ) : (
        <FlatList
          data={notifications}
          renderItem={renderNotification}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContainer}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  headerRight: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
    color: '#333',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#888',
    textAlign: 'center',
    marginTop: 8,
    maxWidth: '80%',
  },
  listContainer: {
    padding: 16,
  },
  notificationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f9f9f9',
    borderRadius: 12,
    marginBottom: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  notificationContent: {
    flex: 1,
    marginLeft: 12,
  },
  senderName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  notificationText: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
  timestamp: {
    fontSize: 12,
    color: '#999',
    marginTop: 4,
  },
  actionButtons: {
    flexDirection: 'column',
    justifyContent: 'center',
    marginLeft: 8,
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#FF4B00',
    marginVertical: 4,
  },
  actionButtonText: {
    color: '#fff',
    fontWeight: '500',
    fontSize: 14,
  },
  acceptButton: {
    backgroundColor: '#FF4B00',
  },
  declineButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#FF4B00',
  },
  declineButtonText: {
    color: '#FF4B00',
  },
  messageButton: {
    backgroundColor: '#2f95dc',
  },
});










