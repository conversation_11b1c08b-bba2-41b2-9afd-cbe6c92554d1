// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Add resolution for common dependencies
config.resolver.extraNodeModules = {
  'react-native': path.resolve(__dirname, 'node_modules/react-native'),
  'react': path.resolve(__dirname, 'node_modules/react'),
  // Add Node.js polyfills
  'stream': require.resolve('stream-browserify'),
  'crypto': require.resolve('crypto-browserify'),
  'http': require.resolve('@tradle/react-native-http'),
  'https': require.resolve('https-browserify'),
  'os': require.resolve('os-browserify/browser'),
  'path': require.resolve('path-browserify'),
  'fs': require.resolve('react-native-level-fs'),
  'buffer': require.resolve('buffer'),
  'events': require.resolve('events'),
  'url': require.resolve('url'),
  'zlib': require.resolve('browserify-zlib'),
  'net': require.resolve('react-native-tcp'),
  'tls': require.resolve('react-native-tcp'),
  'domain': require.resolve('domain-browser'),
  'vm': require.resolve('vm-browserify'),
  'assert': require.resolve('assert'),
  'util': require.resolve('util'),
  'querystring': require.resolve('querystring-es3'),
  'process': require.resolve('process/browser'),
};

// Ensure all .js files are included
config.resolver.sourceExts = ['js', 'jsx', 'json', 'ts', 'tsx', 'cjs', 'mjs'];

// Add watchFolders to include linked dependencies
config.watchFolders = [__dirname];

module.exports = config;
