import { useRouter } from 'expo-router';
import { useState } from 'react';
import {
  <PERSON>ert,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';
import { IconSymbol } from '../components/ui/IconSymbol';
import { useAuth } from '../context/AuthContext';
import { supabase } from '../lib/supabase';

export default function AccountScreen() {
  const router = useRouter();
  const { user, logout } = useAuth();
  const [email, setEmail] = useState(user?.email || '');
  const [phone, setPhone] = useState(user?.phoneNumber || '');

  const handleChangePassword = () => {
    Alert.alert(
      'Change Password',
      'A password reset link will be sent to your email address.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Send Link', 
          onPress: async () => {
            try {
              const { error } = await supabase.auth.resetPasswordForEmail(user?.email || '', {
                redirectTo: 'heatwaves://',
              });
              
              if (error) throw error;
              
              Alert.alert('Success', 'Password reset link has been sent to your email.');
            } catch (error: any) {
              console.error('Password reset error:', error);
              Alert.alert('Error', error.message || 'Failed to send password reset email.');
            }
          }
        }
      ]
    );
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'Delete Account',
      'Are you sure you want to delete your account? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: () => {
            // In a real app, you would call a delete account function here
            Alert.alert(
              'Account Deleted',
              'Your account has been deleted successfully.',
              [
                { 
                  text: 'OK', 
                  onPress: async () => {
                    try {
                      await logout();
                      router.replace('/login');
                    } catch (error) {
                      console.error('Logout error:', error);
                    }
                  }
                }
              ]
            );
          }
        }
      ]
    );
  };

  const handleSaveChanges = () => {
    // In a real app, you would update the user's profile here
    Alert.alert('Success', 'Your account information has been updated.');
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <IconSymbol name="chevron.left" size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Account</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content}>
        <Text style={styles.sectionTitle}>Account Information</Text>
        
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Email</Text>
          <TextInput
            style={styles.input}
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
          />
        </View>
        
        <View style={styles.inputContainer}>
          <Text style={styles.inputLabel}>Phone Number</Text>
          <TextInput
            style={styles.input}
            value={phone}
            onChangeText={setPhone}
            keyboardType="phone-pad"
          />
        </View>
        
        <TouchableOpacity 
          style={styles.saveButton}
          onPress={handleSaveChanges}
        >
          <Text style={styles.saveButtonText}>Save Changes</Text>
        </TouchableOpacity>
        
        <Text style={[styles.sectionTitle, styles.secondSection]}>Security</Text>
        
        <TouchableOpacity 
          style={styles.securityOption}
          onPress={handleChangePassword}
        >
          <View style={styles.securityOptionIcon}>
            <IconSymbol name="lock.fill" size={20} color="#FF4B00" />
          </View>
          <View style={styles.securityOptionText}>
            <Text style={styles.securityOptionTitle}>Change Password</Text>
            <Text style={styles.securityOptionDescription}>Update your password for better security</Text>
          </View>
          <IconSymbol name="chevron.right" size={20} color="#666" />
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.securityOption}
          onPress={() => Alert.alert('Two-Factor Authentication', 'This feature is coming soon.')}
        >
          <View style={styles.securityOptionIcon}>
            <IconSymbol name="shield.fill" size={20} color="#FF4B00" />
          </View>
          <View style={styles.securityOptionText}>
            <Text style={styles.securityOptionTitle}>Two-Factor Authentication</Text>
            <Text style={styles.securityOptionDescription}>Add an extra layer of security</Text>
          </View>
          <IconSymbol name="chevron.right" size={20} color="#666" />
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.deleteAccountButton, styles.secondSection]}
          onPress={handleDeleteAccount}
        >
          <Text style={styles.deleteAccountText}>Delete Account</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
  },
  secondSection: {
    marginTop: 30,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    paddingHorizontal: 15,
    fontSize: 16,
    color: '#333',
    backgroundColor: '#FAFAFA',
  },
  saveButton: {
    backgroundColor: '#FF4B00',
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 10,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  securityOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  securityOptionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 75, 0, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  securityOptionText: {
    flex: 1,
  },
  securityOptionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  securityOptionDescription: {
    fontSize: 14,
    color: '#666',
  },
  deleteAccountButton: {
    borderWidth: 1,
    borderColor: '#FF4B00',
    borderRadius: 8,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 30,
  },
  deleteAccountText: {
    color: '#FF4B00',
    fontSize: 16,
    fontWeight: '500',
  }
});


