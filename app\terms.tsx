import { useRouter } from 'expo-router';
import { useEffect } from 'react';
import {
  Alert,
  Linking,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { IconSymbol } from '../components/ui/IconSymbol';

export default function TermsScreen() {
  const router = useRouter();

  // Log screen view on mount
  useEffect(() => {
    console.log('Terms & Policies screen viewed');
    // In a real app, you would use analytics here
    // analytics.logEvent('screen_view', { screen_name: 'Terms & Policies' });
  }, []);

  const openExternalLink = async (url: string, title: string) => {
    console.log(`Attempting to open ${title} at URL: ${url}`);
    
    try {
      const canOpen = await Linking.canOpenURL(url);
      
      if (canOpen) {
        await Linking.openURL(url);
        console.log(`Successfully opened ${title}`);
      } else {
        console.error(`Cannot open URL: ${url}`);
        Alert.alert(
          'Cannot Open Link',
          'The link could not be opened. Please try again later.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error(`Error opening ${title}:`, error);
      Alert.alert(
        'Error',
        'There was a problem opening the link. Please check your internet connection and try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const handlePrivacyPolicy = () => {
    console.log('Privacy Policy button pressed');
    openExternalLink('https://example.com/privacy-policy', 'Privacy Policy');
  };

  const handleTermsOfService = () => {
    console.log('Terms of Service button pressed');
    openExternalLink('https://example.com/terms-of-service', 'Terms of Service');
  };

  const handleCommunityGuidelines = () => {
    console.log('Community Guidelines button pressed');
    openExternalLink('https://example.com/community-guidelines', 'Community Guidelines');
  };

  const handleCookiePolicy = () => {
    console.log('Cookie Policy button pressed');
    openExternalLink('https://example.com/cookie-policy', 'Cookie Policy');
  };

  const handleBackPress = () => {
    console.log('Back button pressed, returning to previous screen');
    router.back();
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={handleBackPress}
          accessibilityLabel="Go back"
          accessibilityHint="Returns to the previous screen"
        >
          {Platform.OS === 'ios' ? (
            <IconSymbol name="chevron.left" size={24} color="#000" />
          ) : (
            <Ionicons name="chevron-back" size={24} color="#000" />
          )}
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Terms & Policies</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView 
        style={styles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainer}
      >
        <Text style={styles.description}>
          Review our terms and policies to understand how we handle your data and what rules apply when using our app.
        </Text>
        
        <TouchableOpacity 
          style={styles.policyItem}
          onPress={handlePrivacyPolicy}
          accessibilityLabel="Privacy Policy"
          accessibilityHint="Opens the privacy policy in your browser"
        >
          <View style={styles.policyIconContainer}>
            <IconSymbol name="lock.shield.fill" size={20} color="#FF4B00" />
          </View>
          <View style={styles.policyTextContainer}>
            <Text style={styles.policyTitle}>Privacy Policy</Text>
            <Text style={styles.policyDescription}>How we collect, use, and protect your data</Text>
          </View>
          <IconSymbol name="chevron.right" size={20} color="#666" />
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.policyItem}
          onPress={handleTermsOfService}
          accessibilityLabel="Terms of Service"
          accessibilityHint="Opens the terms of service in your browser"
        >
          <View style={styles.policyIconContainer}>
            <IconSymbol name="doc.text.fill" size={20} color="#FF4B00" />
          </View>
          <View style={styles.policyTextContainer}>
            <Text style={styles.policyTitle}>Terms of Service</Text>
            <Text style={styles.policyDescription}>Rules for using our app and services</Text>
          </View>
          <IconSymbol name="chevron.right" size={20} color="#666" />
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.policyItem}
          onPress={handleCommunityGuidelines}
          accessibilityLabel="Community Guidelines"
          accessibilityHint="Opens the community guidelines in your browser"
        >
          <View style={styles.policyIconContainer}>
            <IconSymbol name="person.2.fill" size={20} color="#FF4B00" />
          </View>
          <View style={styles.policyTextContainer}>
            <Text style={styles.policyTitle}>Community Guidelines</Text>
            <Text style={styles.policyDescription}>Standards for interacting with others</Text>
          </View>
          <IconSymbol name="chevron.right" size={20} color="#666" />
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.policyItem}
          onPress={handleCookiePolicy}
          accessibilityLabel="Cookie Policy"
          accessibilityHint="Opens the cookie policy in your browser"
        >
          <View style={styles.policyIconContainer}>
            <IconSymbol name="info.circle.fill" size={20} color="#FF4B00" />
          </View>
          <View style={styles.policyTextContainer}>
            <Text style={styles.policyTitle}>Cookie Policy</Text>
            <Text style={styles.policyDescription}>How we use cookies and similar technologies</Text>
          </View>
          <IconSymbol name="chevron.right" size={20} color="#666" />
        </TouchableOpacity>
        
        <Text style={styles.versionText}>
          App Version: {Platform.OS === 'ios' ? '1.0.0 (10)' : '1.0.0 (10)'} 
        </Text>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 40,
  },
  description: {
    fontSize: 16,
    color: '#666',
    marginBottom: 30,
    lineHeight: 22,
  },
  policyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  policyIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 75, 0, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  policyTextContainer: {
    flex: 1,
  },
  policyTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  policyDescription: {
    fontSize: 14,
    color: '#666',
  },
  versionText: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    marginTop: 40,
    marginBottom: 20,
  },
});


