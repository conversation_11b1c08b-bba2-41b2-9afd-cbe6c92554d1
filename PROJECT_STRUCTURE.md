# Heatwaves Project Structure

## Directory Structure

```
/
├── app/                  # Expo Router app directory
│   ├── (tabs)/           # Tab navigation routes
│   ├── conversation/     # Conversation routes
│   ├── venue/            # Venue routes
│   ├── index.tsx         # Root route
│   └── _layout.tsx       # Root layout
├── assets/               # Static assets
│   ├── images/           # Image assets
│   └── fonts/            # Font assets
├── components/           # Reusable components
│   ├── ui/               # UI components
│   ├── chat/             # Chat-related components
│   ├── call/             # Call-related components
│   └── ...
├── context/              # React context providers
│   └── AuthContext.tsx   # Authentication context
├── hooks/                # Custom React hooks
│   └── useResponsiveStyles.ts
├── services/             # Service modules
│   ├── firebase.ts       # Firebase service
│   ├── AttachmentService.ts
│   ├── MessageService.ts
│   ├── VideoCallService.ts
│   └── VoiceCallService.ts
├── types/                # TypeScript type definitions
│   └── expo-modules.d.ts
├── utils/                # Utility functions
├── app.json              # Expo configuration
├── package.json          # Project dependencies
└── tsconfig.json         # TypeScript configuration
```

## File Organization Guidelines

1. **Components**: All reusable UI components should be placed in the `/components` directory, organized by feature or type.

2. **Services**: All service modules (API calls, data handling, etc.) should be placed in the `/services` directory.

3. **Hooks**: Custom React hooks should be placed in the `/hooks` directory.

4. **Context**: React context providers should be placed in the `/context` directory.

5. **Types**: TypeScript type definitions should be placed in the `/types` directory.

6. **Utils**: Utility functions should be placed in the `/utils` directory.

7. **Assets**: Static assets should be placed in the `/assets` directory.

8. **App**: All Expo Router routes should be placed in the `/app` directory.

## Import Conventions

- Use absolute imports for project files: `import { Button } from '@/components/ui/Button'`
- Use relative imports for files in the same directory: `import { Button } from './Button'`
- Use package imports for external packages: `import { View } from 'react-native'`

## Naming Conventions

- **Components**: PascalCase (e.g., `Button.tsx`)
- **Hooks**: camelCase with `use` prefix (e.g., `useAuth.ts`)
- **Services**: PascalCase with `Service` suffix (e.g., `AuthService.ts`)
- **Utils**: camelCase (e.g., `formatDate.ts`)
- **Context**: PascalCase with `Context` suffix (e.g., `AuthContext.tsx`)