import { useLocalSearch<PERSON>ara<PERSON>, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Image,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import GallerySwiper from 'react-native-gallery-swiper';
import { IconSymbol } from '../../components/ui/IconSymbol';
import { useAuth } from '../../context/AuthContext';
import { supabase } from '../../lib/supabase';

export default function ProfileDetailsScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const { user } = useAuth();
  const [profile, setProfile] = useState<any>(null);

  useEffect(() => {
    if (id) {
      fetchProfile();
    }
  }, [id]);

  async function fetchProfile() {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching profile:', error);
      Alert.alert('Error', 'Could not load profile. Please try again.');
    } else {
      setProfile(data);
    }
  }
  
  // Add the handleEditProfile function
  const handleEditProfile = () => {
    // Check if this is the current user's profile
    if (user?.id === id) {
      // Navigate to edit profile screen
      router.push('/(auth)/edit-profile');
    } else {
      // For other users' profiles, we might want to show a message
      // or navigate to a different screen
      console.log('Cannot edit another user\'s profile');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <IconSymbol name="chevron.left" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Profile</Text>

      </View>
      
      <ScrollView style={styles.content}>
    {profile ? (
      <View>
        {profile.images && profile.images.length > 0 ? (
          <GallerySwiper
            images={profile.images.map((img: string) => ({ uri: img }))}
            style={styles.gallerySwiper}
          />
        ) : profile.avatar_url ? (
          <Image source={{ uri: profile.avatar_url }} style={styles.profileImage} />
        ) : null}
        <Text style={styles.profileName}>{profile.full_name}</Text>
        {profile.gender && <Text style={styles.profileDetail}>Gender: {profile.gender}</Text>}
        {profile.dob && <Text style={styles.profileDetail}>Date of Birth: {new Date(profile.dob).toLocaleDateString()}</Text>}
        {profile.age && <Text style={styles.profileDetail}>Age: {profile.age}</Text>}
        <Text style={styles.profileBio}>{profile.bio}</Text>
        {profile.hobbies && profile.hobbies.length > 0 && (
          <View style={styles.detailSection}>
            <Text style={styles.detailTitle}>Hobbies:</Text>
            <Text style={styles.profileDetail}>{profile.hobbies.join(', ')}</Text>
          </View>
        )}
        {profile.social_media_links && Object.keys(profile.social_media_links).length > 0 && (
          <View style={styles.detailSection}>
            <Text style={styles.detailTitle}>Social Media:</Text>
            {Object.entries(profile.social_media_links).map(([platform, link]) => (
              <Text key={platform} style={styles.profileDetail}>{platform}: {link}</Text>
            ))}
          </View>
        )}
      </View>
    ) : (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#FFFFFF" />
        <Text style={styles.text}>Loading profile...</Text>
    </View>
    )}
  </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  text: {
    color: '#FFFFFF',
    fontSize: 16,
  },

  profileName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  profileBio: {
    fontSize: 16,
    color: '#CCCCCC',
    marginBottom: 16,
  },
  profileImage: {
    width: '100%',
    height: 300,
    borderRadius: 8,
    marginBottom: 16,
  },
  gallerySwiper: {
    height: 300,
    width: '100%',
    marginBottom: 16,
  },
  profileDetail: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 4,
  },
  detailSection: {
    marginTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#333333',
    paddingTop: 16,
  },
  detailTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});



