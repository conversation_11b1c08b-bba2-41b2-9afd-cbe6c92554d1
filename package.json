{"name": "heatwaves", "main": "index.js", "version": "1.0.0", "scripts": {"start": "expo start", "backend": "node ./backend/server.js", "dev": "concurrently \"npm run start\" \"npm run backend\"", "reset-project": "node ./scripts/reset-project.js", "cleanup": "node ./scripts/cleanup.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@maplibre/maplibre-react-native": "^10.1.4", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.4.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@supabase/supabase-js": "^2.49.4", "@tradle/react-native-http": "^2.0.1", "@turf/distance": "^7.2.0", "@turf/helpers": "^7.2.0", "@turf/length": "^7.2.0", "@turf/nearest-point-on-line": "^7.2.0", "assert": "^2.1.0", "axios": "^1.9.0", "base64-arraybuffer": "^1.0.2", "bn.js": "^5.2.2", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "debounce": "^2.2.0", "domain-browser": "^5.7.0", "dotenv": "^16.5.0", "events": "^3.3.0", "expo": "~53.0.7", "expo-ads-admob": "^13.0.0", "expo-av": "~13.0.0", "expo-blur": "~14.1.4", "expo-camera": "~16.1.6", "expo-constants": "~17.1.5", "expo-contacts": "~14.2.3", "expo-dev-client": "~5.1.8", "expo-document-picker": "~13.1.5", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.6", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.4", "expo-location": "~18.1.4", "expo-router": "~5.0.6", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "https-browserify": "^1.0.0", "is-arrayish": "^0.3.2", "lucide-react": "^0.511.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "querystring-es3": "^0.2.1", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-dotenv": "^3.4.11", "react-native-gallery-swiper": "^1.26.5", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "~2.24.0", "react-native-level-fs": "^3.0.1", "react-native-maps": "1.20.1", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-tcp": "^4.0.0", "react-native-web": "~0.20.0", "react-native-webrtc": "^1.100.0", "react-native-webview": "13.13.5", "sha.js": "^2.4.11", "stream-browserify": "^3.0.0", "url": "^0.11.4", "util": "^0.12.5", "vm-browserify": "^1.1.2", "webidl-conversions": "^7.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@tsconfig/react-native": "^3.0.5", "@types/axios": "^0.9.36", "@types/babel__core": "^7.20.5", "@types/babel__generator": "^7.27.0", "@types/babel__template": "^7.4.4", "@types/babel__traverse": "^7.20.7", "@types/expo__vector-icons": "^9.0.1", "@types/jest": "^29.5.14", "@types/react": "^19.0.0", "@types/react-native": "^0.72.8", "babel-plugin-module-resolver": "^5.0.2", "concurrently": "^9.1.2", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "^5.8.3"}, "private": true}