@echo off
echo Setting up Java environment...

REM Check for Java 11 installation
if exist "C:\Program Files\Java\jdk-11" (
    setx JAVA_HOME "C:\Program Files\Java\jdk-11"
    echo JAVA_HOME set to C:\Program Files\Java\jdk-11
    goto :success
)

if exist "C:\Program Files\Eclipse Adoptium\jdk-11" (
    setx JAVA_HOME "C:\Program Files\Eclipse Adoptium\jdk-11"
    echo JAVA_HOME set to C:\Program Files\Eclipse Adoptium\jdk-11
    goto :success
)

REM Check for Java 8 installation
if exist "C:\Program Files\Java\jdk1.8.0" (
    setx JAVA_HOME "C:\Program Files\Java\jdk1.8.0"
    echo JAVA_HOME set to C:\Program Files\Java\jdk1.8.0
    goto :success
)

if exist "C:\Program Files\Eclipse Adoptium\jdk-8" (
    setx JAVA_HOME "C:\Program Files\Eclipse Adoptium\jdk-8"
    echo JAVA_HOME set to C:\Program Files\Eclipse Adoptium\jdk-8
    goto :success
)

echo No compatible Java version found. Please install JDK 11 or JDK 8.
echo You can download it from: https://adoptium.net/
goto :end

:success
echo Java setup complete. Please restart your command prompt for changes to take effect.

:end