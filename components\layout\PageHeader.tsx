import React, { ReactNode } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { IconSymbol } from '../ui/IconSymbol';
import { useResponsiveStyles } from '../../hooks/useResponsiveStyles';

interface PageHeaderProps {
  title: string;
  leftComponent?: ReactNode;
  rightComponent?: ReactNode;
  onBackPress?: () => void;
  showBackButton?: boolean;
}

export function PageHeader({
  title,
  leftComponent,
  rightComponent,
  onBackPress,
  showBackButton = false,
}: PageHeaderProps) {
  const { spacing, fontSizes, dimensions } = useResponsiveStyles();
  
  // Define colors directly
  const colors = {
    tint: '#007AFF',
    text: '#000000',
    surface: '#F2F2F7',
  };
  
  return (
    <View style={[
      styles.headerContent,
      { paddingHorizontal: spacing.md, paddingVertical: spacing.sm }
    ]}>
      <View style={styles.leftContainer}>
        {showBackButton && (
          <TouchableOpacity 
            style={[
              styles.headerButton,
              { 
                marginRight: spacing.sm,
                width: dimensions.iconSize * 1.5,
                height: dimensions.iconSize * 1.5,
                borderRadius: dimensions.iconSize * 0.75,
                backgroundColor: colors.surface,
              }
            ]}
            onPress={onBackPress}
          >
            <IconSymbol 
              size={dimensions.iconSize * 0.8} 
              name="chevron.left" 
              color={colors.tint} 
            />
          </TouchableOpacity>
        )}
        {leftComponent}
      </View>
      
      <Text style={[
        styles.headerTitle,
        { 
          fontSize: fontSizes.xl,
          color: colors.text 
        }
      ]}>
        {title}
      </Text>
      
      <View style={styles.rightContainer}>
        {rightComponent}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  leftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
  },
});
