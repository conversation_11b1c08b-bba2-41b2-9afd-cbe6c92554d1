import React, { useEffect } from 'react';
import { View, StyleSheet, useColorScheme } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withSequence,
  withTiming,
  Easing,
  interpolate,
} from 'react-native-reanimated';
import { ThemedText } from './ui/ThemedText';
import { IconSymbol } from './ui/IconSymbol';

export function AppLogo({ size = 120, showText = true }) {
  const colorScheme = useColorScheme();
  const flameAnimation = useSharedValue(1);
  const flameRotation = useSharedValue(0);
  
  useEffect(() => {
    // Create a subtle pulsing animation for the flame
    flameAnimation.value = withRepeat(
      withSequence(
        withTiming(1.1, { duration: 800, easing: Easing.inOut(Easing.ease) }),
        withTiming(1, { duration: 800, easing: Easing.inOut(Easing.ease) })
      ),
      -1, // Infinite repeat
      true // Reverse
    );
    
    // Create a subtle rotation animation
    flameRotation.value = withRepeat(
      withSequence(
        withTiming(-0.05, { duration: 1000, easing: Easing.inOut(Easing.ease) }),
        withTiming(0.05, { duration: 1000, easing: Easing.inOut(Easing.ease) })
      ),
      -1, // Infinite repeat
      true // Reverse
    );
  }, []);
  
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: flameAnimation.value },
      { rotate: `${flameRotation.value}rad` }
    ],
    opacity: interpolate(flameAnimation.value, [1, 1.1], [0.9, 1]),
  }));
  
  // Use a different color based on the theme
  const flameColor = colorScheme === 'dark' ? '#FF6B00' : '#FF4B00';
  
  return (
    <View style={styles.container}>
      <View style={[styles.iconContainer, { width: size, height: size }]}>
        <Animated.View style={[styles.flameContainer, animatedStyle]}>
          <IconSymbol 
            name="flame.fill" 
            size={size * 0.8} 
            color={flameColor} 
            style={{}}
          />
        </Animated.View>
      </View>
      
      {showText && (
        <View style={styles.textContainer}>
          <ThemedText style={[styles.appName]}>
            Heatwaves
          </ThemedText>
          <ThemedText style={styles.tagline}>
            Feel the heat. Connect. Discover.
          </ThemedText>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  iconContainer: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  flameContainer: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
  textContainer: {
    alignItems: 'center',
    marginTop: 16,
  },
  appName: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  tagline: {
    fontSize: 16,
    opacity: 0.7,
  },
});












