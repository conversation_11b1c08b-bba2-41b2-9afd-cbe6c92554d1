# Android Development Build Guide

## Prerequisites
- Node.js installed
- Android Studio installed
- Android SDK configured
- A physical Android device or emulator

## Setup Steps

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Generate the Android development build**
   ```bash
   npx expo prebuild --platform android
   ```

3. **Start the development server**
   ```bash
   npx expo start --dev-client
   ```

## Building and Running

### Using Android Studio

1. Open the `android` folder in Android Studio
2. Wait for Gradle sync to complete
3. Connect your Android device or start an emulator
4. Click the "Run" button (green triangle)

### Using Command Line

1. Connect your Android device via USB (ensure USB debugging is enabled)
2. Run the following command:
   ```bash
   npx expo run:android
   ```

## Troubleshooting

### Common Issues

1. **Gradle sync failed**
   - Ensure you have the correct Android SDK installed
   - Check internet connection for dependency downloads
   - Run `./gradlew clean` in the android directory

2. **Build errors**
   - Check that all dependencies are properly installed
   - Ensure your Android SDK versions match the ones in build.gradle
   - Run `npm install` to ensure all JS dependencies are installed

3. **Device not detected**
   - Ensure USB debugging is enabled on your device
   - Try a different USB cable or port
   - Run `adb devices` to check if your device is recognized

## Development Workflow

1. Make changes to your JavaScript code
2. The app will automatically reload with your changes
3. For native code changes, you'll need to rebuild the app:
   ```bash
   npx expo run:android
   ```

## Creating a Release Build

When you're ready to create a release build:

1. Update version in `android/app/build.gradle`
2. Configure signing keys in `android/app/build.gradle`
3. Run:
   ```bash
   cd android
   ./gradlew assembleRelease
   ```
4. Find the APK in `android/app/build/outputs/apk/release/`