import { Stack } from 'expo-router';

export default function ProfileDetailsLayout() {
  return (
    <Stack>
      <Stack.Screen
        name="index"
        options={{
          headerShown: false,
        }}
      />
      {/* This route is causing the warning - make sure it's needed */}
      {/* If you're using this route, keep it, otherwise remove it */}
      <Stack.Screen
        name="friend-requests"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="[id]"
        options={{
          headerShown: false,
        }}
      />
    </Stack>
  );
}
