# Welcome to Heatwaves 👋

This is an [Expo](https://expo.dev) project created with [`create-expo-app`](https://www.npmjs.com/package/create-expo-app).

## Get started

1. Install dependencies

   ```bash
   npm install
   ```

2. Start the app

   ```bash
   npx expo start
   ```

In the output, you'll find options to open the app in a

- [development build](https://docs.expo.dev/develop/development-builds/introduction/)
- [Android emulator](https://docs.expo.dev/workflow/android-studio-emulator/)
- [iOS simulator](https://docs.expo.dev/workflow/ios-simulator/)
- [Expo Go](https://expo.dev/go), a limited sandbox for trying out app development with Expo

You can start developing by editing the files inside the **app** directory. This project uses [file-based routing](https://docs.expo.dev/router/introduction).

## App Icon Design Guidelines

### Fire-themed Icon with "Heatwaves" Text

1. **Icon Design:**
   - Create a circular icon with a gradient background (from #FF4B00 to #FF6B00)
   - Add a stylized flame symbol in the center (white or light orange)
   - Include "Heatwaves" text at the bottom of the circle in a modern, bold font
   - Keep the design simple and recognizable at small sizes

2. **Required Icon Files:**
   - `assets/images/icon.png` (1024x1024px) - Main app icon
   - `assets/images/adaptive-icon.png` (1024x1024px) - For Android adaptive icons
   - `assets/images/splash-icon.png` (200x200px) - For splash screen
   - `assets/images/favicon.png` (48x48px) - For web

3. **Design Specifications:**
   - Primary color: #FF4B00 (vibrant orange-red)
   - Secondary color: #FF6B00 (softer orange)
   - Text color: White (#FFFFFF)
   - Font: Sans-serif, bold weight
   - Flame design: Stylized, minimalist approach

4. **After creating the icons:**
   - Place them in the appropriate directories
   - Run `npx expo prebuild --clean` to regenerate native files with the new icons

## Flame Icon Improvement

To improve the flame icon:

1. Remove the grid background completely
2. Make the flame shape more dynamic with smoother curves
3. Use a gradient from bright orange (#FF4B00) to yellow-orange (#FFAA00)
4. Add a subtle glow effect around the edges
5. Save as a transparent PNG file named `flame-overlay.png`
6. Place in the `assets/images/` directory

The improved flame icon should:
- Have completely transparent background
- Feature a two-tone flame design (orange outer flame, yellow-orange inner flame)
- Have smooth, natural-looking curves
- Include a subtle glow effect for better visibility on different backgrounds

## Get a fresh project

When you're ready, run:

```bash
npm run reset-project
```

This command will move the starter code to the **app-example** directory and create a blank **app** directory where you can start developing.

## Learn more

To learn more about developing your project with Expo, look at the following resources:

- [Expo documentation](https://docs.expo.dev/): Learn fundamentals, or go into advanced topics with our [guides](https://docs.expo.dev/guides).
- [Learn Expo tutorial](https://docs.expo.dev/tutorial/introduction/): Follow a step-by-step tutorial where you'll create a project that runs on Android, iOS, and the web.

## Join the community

Join our community of developers creating universal apps.

- [Expo on GitHub](https://github.com/expo/expo): View our open source platform and contribute.
- [Discord community](https://chat.expo.dev): Chat with Expo users and ask questions.

