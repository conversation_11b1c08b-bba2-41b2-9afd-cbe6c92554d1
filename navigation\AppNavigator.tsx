import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useAuth } from '../context/AuthContext';
import AuthStack from './AuthStack';
import MainTabs from './MainTabs';

const Tab = createBottomTabNavigator();

export default function AppNavigator() {
  const { user } = useAuth();
  
  // If user is not logged in, show auth screens
  // Otherwise, show main app screens
  return user ? <MainTabs /> : <AuthStack />;
}