import DateTimePicker from '@react-native-community/datetimepicker';
import * as ImagePicker from 'expo-image-picker';
import { useRouter } from 'expo-router';
import { useState } from 'react';
import {
    Alert,
    Image,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { IconSymbol } from '../../components/ui/IconSymbol';
import { Colors } from '../../constants/Colors';
import { useAuth } from '../../context/AuthContext';


// Define extended User type to include the additional fields
interface ExtendedUser {
  id: string;
  email: string;
  displayName: string;
  photoURL?: string;
  phoneNumber?: string;
  bio?: string;
  gender?: string;
  preference?: string;
  photos?: string[];
  coinBalance?: number;
  // Additional fields
  occupation?: string;
  education?: string;
  location?: string;
  birthdate?: string;
}

export default function EditProfileScreen() {
  const { user, updateUserProfile } = useAuth();
  const router = useRouter();
  const insets = useSafeAreaInsets();
  
  // State for form fields
  const [name, setName] = useState(user?.displayName || '');
  const [bio, setBio] = useState(user?.bio || '');
  const [gender, setGender] = useState(user?.gender || '');
  const [phoneNumber, setPhoneNumber] = useState(user?.phoneNumber || '');
  const [occupation, setOccupation] = useState((user as any)?.occupation || '');
  const [education, setEducation] = useState((user as any)?.education || '');
  const [location, setLocation] = useState((user as any)?.location || '');
  const [birthdate, setBirthdate] = useState(
    (user as any)?.birthdate ? new Date((user as any).birthdate) : null
  );
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [profileImage, setProfileImage] = useState(user?.photoURL || '');
  // Add state for hobbies
  const [hobbies, setHobbies] = useState<string[]>((user as any)?.hobbies || []);
  const [newHobby, setNewHobby] = useState('');

  // Function to pick image from gallery
  const pickImage = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    
    if (status !== 'granted') {
      Alert.alert('Permission needed', 'Please grant permission to access your photos');
      return;
    }
    
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [1, 1],
      quality: 0.8,
    });
    
    if (!result.canceled && result.assets && result.assets.length > 0) {
      setProfileImage(result.assets[0].uri);
    }
  };
  
  // Function to handle date change with proper typing
  const onDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(Platform.OS === 'ios');
    if (selectedDate) {
      setBirthdate(selectedDate);
    }
  };
  
  // Function to show date picker based on platform
  const showDatePickerModal = () => {
    setShowDatePicker(true);
  };
  
  const handleSave = async () => {
    try {
      // Create an object that matches the User type
      const updatedProfile: Partial<ExtendedUser> = {
        displayName: name,
        bio,
        gender,
        phoneNumber,
        photoURL: profileImage || undefined,
      };
      
      // Add hobbies to the user data
      (updatedProfile as any).hobbies = hobbies;
      
      // Add birthdate if valid
      if (birthdate) {
        (updatedProfile as any).birthdate = birthdate.toISOString();
      }
      
      // Add other custom fields
      (updatedProfile as any).occupation = occupation;
      (updatedProfile as any).education = education;
      (updatedProfile as any).location = location;
      
      // Update the profile with the standard fields
      // We'll use type assertion to bypass TypeScript checking
      await updateUserProfile(updatedProfile as any);
      
      Alert.alert('Success', 'Profile updated successfully');
      router.back();
    } catch (error) {
      Alert.alert('Error', 'Failed to update profile');
    }
  };
  
  // Function to add a new hobby
  const addHobby = () => {
    if (newHobby.trim() === '') return;
    
    // Add the new hobby to the array
    setHobbies([...hobbies, newHobby.trim()]);
    // Clear the input
    setNewHobby('');
  };

  // Function to remove a hobby
  const removeHobby = (index: number) => {
    const updatedHobbies = [...hobbies];
    updatedHobbies.splice(index, 1);
    setHobbies(updatedHobbies);
  };

  return (
    <KeyboardAvoidingView 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}
    >
      <View style={[styles.container, { paddingTop: insets.top }]}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <IconSymbol name="chevron.left" size={24} color={Colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Edit Profile</Text>
          <TouchableOpacity onPress={handleSave} style={styles.saveButton}>
            <Text style={styles.saveButtonText}>Save</Text>
          </TouchableOpacity>
        </View>
        
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.profileImageContainer}>
            <Image
            source={{ uri: profileImage || 'https://via.placeholder.com/150' }}
            style={[styles.profileImage, styles.profileImageBorder]}
          />
            <TouchableOpacity style={styles.changePhotoButton} onPress={pickImage}>
              <Text style={styles.changePhotoText}>Change Photo</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Name</Text>
            <TextInput
              style={styles.input}
              value={name}
              onChangeText={setName}
              placeholder="Your name"
            />
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Phone Number</Text>
            <TextInput
              style={styles.input}
              value={phoneNumber}
              onChangeText={setPhoneNumber}
              placeholder="Your phone number"
              keyboardType="phone-pad"
            />
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Date of Birth</Text>
            <TouchableOpacity onPress={showDatePickerModal} style={styles.input}>
              <Text style={{ color: birthdate ? '#000' : '#999' }}>
                {birthdate ? birthdate.toLocaleDateString('en-GB') : 'DD/MM/YYYY'}
              </Text>
            </TouchableOpacity>
            {showDatePicker && (
              <DateTimePicker
                value={birthdate || new Date()}
                mode="date"
                display="default"
                onChange={onDateChange}
              />
            )}
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Location</Text>
            <TextInput
              style={styles.input}
              value={location}
              onChangeText={setLocation}
              placeholder="Your location"
            />
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Occupation</Text>
            <TextInput
              style={styles.input}
              value={occupation}
              onChangeText={setOccupation}
              placeholder="Your occupation"
            />
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Education</Text>
            <TextInput
              style={styles.input}
              value={education}
              onChangeText={setEducation}
              placeholder="Your education"
            />
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Bio</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={bio}
              onChangeText={setBio}
              placeholder="Tell us about yourself"
              multiline
              numberOfLines={4}
            />
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Gender</Text>
            <View style={styles.optionsContainer}>
              {['Male', 'Female', 'Non-binary', 'Prefer not to say'].map((option) => (
                <TouchableOpacity 
                  key={option}
                  style={[
                    styles.optionButton,
                    gender === option && styles.selectedOption
                  ]}
                  onPress={() => setGender(option)}
                >
                  <Text style={[
                    styles.optionText,
                    gender === option && styles.selectedOptionText
                  ]}>
                    {option}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
          
          <View style={styles.formGroup}>
            <Text style={styles.label}>Hobbies</Text>
            <View style={styles.hobbiesContainer}>
              {hobbies.map((hobby, index) => (
                <View key={index} style={styles.hobbyTag}>
                  <Text style={styles.hobbyText}>{hobby}</Text>
                  <TouchableOpacity 
                    onPress={() => removeHobby(index)}
                    style={styles.removeHobbyButton}
                  >
                    <IconSymbol name="xmark" size={12} color="#666" />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
            
            <View style={styles.addHobbyContainer}>
              <TextInput
                style={styles.hobbyInput}
                value={newHobby}
                onChangeText={setNewHobby}
                placeholder="Add a hobby"
                returnKeyType="done"
                onSubmitEditing={addHobby}
              />
              <TouchableOpacity 
                style={styles.addHobbyButton}
                onPress={addHobby}
              >
                <Text style={styles.addHobbyButtonText}>Add</Text>
              </TouchableOpacity>
            </View>
          </View>
          
          <View style={{ height: 40 }} />
        </ScrollView>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  saveButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  saveButtonText: {
    color: Colors.primary,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  profileImageContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  profileImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  profileImageBorder: {
    borderWidth: 3,
    borderColor: Colors.primary,
  },
  changePhotoButton: {
    padding: 8,
  },
  changePhotoText: {
    color: Colors.primary,
    fontWeight: '600',
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 10,
    justifyContent: 'space-between',
  },
  optionButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    marginBottom: 12,
    width: '48%',
    alignItems: 'center',
    backgroundColor: '#F8F8F8',
  },
  selectedOption: {
    borderColor: '#FF4B00',
    backgroundColor: 'rgba(255, 75, 0, 0.1)',
  },
  optionText: {
    fontSize: 16,
    color: '#666',
  },
  selectedOptionText: {
    color: '#FF4B00',
    fontWeight: '600',
  },
  inputText: {
    fontSize: 16,
    color: '#333',
  },
  placeholderText: {
    fontSize: 16,
    color: '#999',
  },
  hobbiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  hobbyTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0F0F0',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
  },
  hobbyText: {
    color: '#333',
    fontSize: 14,
    marginRight: 4,
  },
  removeHobbyButton: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#E0E0E0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  addHobbyContainer: {
    flexDirection: 'row',
    marginTop: 12,
  },
  hobbyInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginRight: 8,
  },
  addHobbyButton: {
    backgroundColor: Colors.primary,
    borderRadius: 8,
    paddingHorizontal: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addHobbyButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  helperText: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  errorText: {
    fontSize: 12,
    color: '#FF0000',
    marginTop: 4,
  },
});























