// Minimal chat service implementation

export interface ChatParticipant {
  id: string;
  displayName: string;
  photoURL?: string;
  lastSeen?: number;
  isOnline: boolean;
}

export interface Conversation {
  id: string;
  participants: string[];
  lastMessage?: {
    text: string;
    timestamp: number;
  };
  updatedAt: number;
  unreadCount: number;
}

// Get conversations for a user
export const getConversations = async (userId: string): Promise<Conversation[]> => {
  // Return empty array for now
  return [];
};

// Get a participant by ID
export const getParticipant = async (participantId: string): Promise<ChatParticipant | null> => {
  return {
    id: participantId,
    displayName: `User ${participantId.substring(0, 5)}`,
    isOnline: false
  };
};

// Add a new participant
export const addParticipant = async (participant: ChatParticipant): Promise<void> => {
  console.log('Adding participant:', participant);
};

// Create a new conversation
export const createConversation = async (
  userId1: string,
  userId2: string,
  initialMessage?: string
): Promise<Conversation> => {
  return {
    id: `conv_${Date.now()}`,
    participants: [userId1, userId2],
    updatedAt: Date.now(),
    unreadCount: 0
  };
};

