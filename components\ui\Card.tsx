import React from 'react';
import { Dimensions, Platform, Pressable, StyleSheet, useColorScheme, View, ViewProps, ViewStyle } from 'react-native';
import Animated, { useAnimatedStyle, withTiming } from 'react-native-reanimated';
import { Colors } from '../../constants/Colors';

// Get screen dimensions for responsive sizing
const { width } = Dimensions.get('window');

interface CardProps extends ViewProps {
  onPress?: () => void;
  elevation?: 'none' | 'small' | 'medium' | 'large';
  variant?: 'filled' | 'outlined';
  radius?: 'small' | 'medium' | 'large' | 'full';
  width?: number | string; // Allow custom width
  height?: number | string; // Allow custom height
}

export function Card({
  children,
  style,
  onPress,
  elevation = 'small',
  variant = 'filled',
  radius = 'medium',
  width: customWidth,
  height: customHeight,
  ...rest
}: CardProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [isPressed, setIsPressed] = React.useState(false);
  
  // Get border radius based on size
  const getBorderRadius = () => {
    switch (radius) {
      case 'small': return 8;
      case 'medium': return 16;
      case 'large': return 24;
      case 'full': return 9999;
      default: return 16;
    }
  };
  
  // Get shadow based on elevation and platform
  const getShadowStyle = () => {
    if (isDark || elevation === 'none') return {};
    
    // Different shadow implementations for iOS and Android
    if (Platform.OS === 'ios') {
      switch (elevation) {
        case 'small':
          return {
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.1,
            shadowRadius: 2,
          };
        case 'medium':
          return {
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.15,
            shadowRadius: 4,
          };
        case 'large':
          return {
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.2,
            shadowRadius: 8,
          };
        default:
          return {};
      }
    } else {
      // Android elevation
      switch (elevation) {
        case 'small':
          return { elevation: 2 };
        case 'medium':
          return { elevation: 4 };
        case 'large':
          return { elevation: 8 };
        default:
          return {};
      }
    }
  };
  
  // Animated style for press feedback
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { scale: withTiming(isPressed ? 0.98 : 1, { duration: 150 }) }
      ],
      opacity: withTiming(isPressed ? 0.9 : 1, { duration: 150 })
    };
  });
  
  // Responsive width calculation
  const getResponsiveWidth = () => {
    if (customWidth) {
      // If it's a number, return it directly
      if (typeof customWidth === 'number') return customWidth;
      // If it's a percentage string, return it as a valid DimensionValue
      if (typeof customWidth === 'string' && customWidth.endsWith('%')) 
        return customWidth as `${number}%`;
      // For other strings, return undefined
      return undefined;
    }
    
    // Default responsive width based on screen size
    return width > 600 ? '85%' as `${number}%` : '92%' as `${number}%`;
  };
  
  // Fix height type to ensure it's compatible with ViewStyle
  const getHeight = () => {
    if (typeof customHeight === 'number') return customHeight;
    // Only allow percentage strings which are valid DimensionValue
    if (typeof customHeight === 'string' && customHeight.endsWith('%')) return customHeight as `${number}%`;
    return undefined; // Default to undefined if not a valid type
  };
  
  // Create a properly typed style object
  const cardStyle: ViewStyle = {
    backgroundColor: variant === 'filled' 
      ? (isDark ? Colors.dark.card : Colors.light.card)
      : 'transparent',
    borderWidth: variant === 'outlined' ? 1 : 0,
    borderColor: isDark ? Colors.dark.border : Colors.light.border,
    borderRadius: getBorderRadius(),
    width: getResponsiveWidth(),
    height: getHeight(),
    ...getShadowStyle(),
  };
  
  if (onPress) {
    return (
      <Pressable
        onPressIn={() => setIsPressed(true)}
        onPressOut={() => setIsPressed(false)}
        onPress={onPress}
        style={{ alignSelf: 'center' }} // Center the card
      >
        <Animated.View 
          style={[styles.card, cardStyle, animatedStyle, style]} 
          {...rest}
        >
          {children}
        </Animated.View>
      </Pressable>
    );
  }
  
  return (
    <View style={[styles.card, cardStyle, { alignSelf: 'center' }, style]} {...rest}>
      {children}
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    padding: 20,
    marginVertical: 10,
    borderRadius: 16, // Added for rounded corners
    overflow: 'hidden',
    // Center the card
    alignSelf: 'center',
  },
});




