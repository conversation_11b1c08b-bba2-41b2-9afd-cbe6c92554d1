// Minimal message service

export interface Message {
  id: string;
  text: string;
  senderId: string;
  timestamp: number;
  isStarred?: boolean;
  type?: string;
  media?: {
    type: string;
    uri: string;
  };
  metadata?: {
    type: string;
    amount?: number;
    action?: string;
  };
}

export const useMessages = (conversationId: string) => {
  const messages: Message[] = [];
  const isLoading = false;

  const sendTextMessage = async (text: string, replyingTo?: Message) => {
    console.log(`Sending message to ${conversationId}: ${text}`);
    return {
      id: `msg_${Date.now()}`,
      text,
      senderId: 'currentUser',
      timestamp: Date.now()
    };
  };

  const sendImageMessage = async (imageUri: string) => {
    console.log(`Sending image to ${conversationId}: ${imageUri}`);
    return {
      id: `msg_${Date.now()}`,
      text: '[Image]',
      senderId: 'currentUser',
      timestamp: Date.now()
    };
  };

  const sendDocumentMessage = async (documentUri: string) => {
    console.log(`Sending document to ${conversationId}: ${documentUri}`);
    return {
      id: `msg_${Date.now()}`,
      text: '[Document]',
      senderId: 'currentUser',
      timestamp: Date.now()
    };
  };

  const deleteMessage = async (messageId: string) => {
    console.log(`Deleting message ${messageId}`);
    return true;
  };

  const toggleStarMessage = async (messageId: string) => {
    console.log(`Toggling star for message ${messageId}`);
    return true;
  };

  return {
    messages,
    isLoading,
    sendTextMessage,
    sendImageMessage,
    sendDocumentMessage,
    deleteMessage,
    toggleStarMessage
  };
};

