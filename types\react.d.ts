// This helps TypeScript understand React 19's module structure
declare module 'react/useCallback' {
  export default function useCallback<T extends (...args: any[]) => any>(
    callback: T,
    deps: ReadonlyArray<any>
  ): T;
}

declare module 'react/useEffect' {
  export default function useEffect(
    effect: () => void | (() => void),
    deps?: ReadonlyArray<any>
  ): void;
}

declare module 'react/useMemo' {
  export default function useMemo<T>(
    factory: () => T,
    deps: ReadonlyArray<any>
  ): T;
}

declare module 'react/useRef' {
  export default function useRef<T>(initialValue: T): { current: T };
  export default function useRef<T>(initialValue: T | null): { current: T | null };
}

declare module 'react/useState' {
  export default function useState<S>(
    initialState: S | (() => S)
  ): [S, (newState: S | ((prevState: S) => S)) => void];
}

declare module 'react/memo' {
  export default function memo<P extends object>(
    Component: React.FunctionComponent<P>,
    propsAreEqual?: (prevProps: Readonly<P>, nextProps: Readonly<P>) => boolean
  ): React.FunctionComponent<P>;
}
