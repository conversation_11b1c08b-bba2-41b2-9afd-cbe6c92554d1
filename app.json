{"expo": {"name": "Heatwaves", "slug": "heatwaves", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/default-icon.png", "scheme": "heatwaves", "userInterfaceStyle": "light", "newArchEnabled": true, "packagerOpts": {"config": "metro.config.js"}, "ios": {"supportsTablet": true, "bundleIdentifier": "heat.waves", "googleServicesFile": "./GoogleService-Info.plist", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "NSLocationWhenInUseUsageDescription": "We need your location to find hangout spots near you", "LSApplicationQueriesSchemes": ["razorpay", "paytm", "phonepe", "gpay", "bhim", "upi"]}, "config": {"googleMapsApiKey": "AIzaSyCOoluAx0PkOq7iIztZOj510XEsGmWx8uQ"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "heat.waves", "permissions": ["INTERNET", "ACCESS_FINE_LOCATION"], "intentFilters": [{"action": "VIEW", "data": [{"scheme": "upi", "host": "*"}], "category": ["BROWSABLE", "DEFAULT"]}], "queries": {"canOpenURL": ["paytm", "phonepe", "gpay", "bhim", "upi"]}}}}