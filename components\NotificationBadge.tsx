import React from 'react';
import { StyleSheet, Text, View } from 'react-native';

interface NotificationBadgeProps {
  count: number;
  size?: 'small' | 'medium' | 'large';
  color?: string;
}

export const NotificationBadge: React.FC<NotificationBadgeProps> = ({ 
  count, 
  size = 'medium',
  color = '#FF5722' 
}) => {
  if (count <= 0) return null;
  
  // Determine badge size
  const badgeSize = {
    small: 16,
    medium: 20,
    large: 24
  }[size];
  
  // Determine font size
  const fontSize = {
    small: 10,
    medium: 12,
    large: 14
  }[size];
  
  // For counts over 99, show 99+
  const displayCount = count > 99 ? '99+' : count.toString();
  
  // Adjust width for double digits or "99+"
  const width = displayCount.length > 1 ? badgeSize * 1.2 : badgeSize;
  
  return (
    <View 
      style={[
        styles.badge, 
        { 
          backgroundColor: color,
          width: width,
          height: badgeSize,
          borderRadius: badgeSize / 2
        }
      ]}
    >
      <Text style={[styles.text, { fontSize }]}>{displayCount}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  badge: {
    position: 'absolute',
    top: -5,
    right: -5,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  text: {
    color: 'white',
    fontWeight: 'bold',
  }
});

export default NotificationBadge;