@echo off
echo Current Java location: 
where java

echo.
echo Setting JAVA_HOME to the correct path...

REM Set JAVA_HOME to the correct Java 8 path
set JAVA_HOME=C:\Program Files (x86)\Common Files\Oracle\Java\java8path
setx JAVA_HOME "C:\Program Files (x86)\Common Files\Oracle\Java\java8path"

echo New JAVA_HOME: %JAVA_HOME%

REM Verify Java version
"%JAVA_HOME%\java" -version

echo.
echo Now trying to build the Android app...
cd android
call gradlew.bat assembleDebug

cd ..

if exist "android\app\build\outputs\apk\debug\app-debug.apk" (
  echo Build successful! APK created.
  echo Installing on connected device...
  adb install -r android\app\build\outputs\apk\debug\app-debug.apk
) else (
  echo Build failed or APK not found at the expected location.
)