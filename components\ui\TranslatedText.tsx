import React from 'react';
import { Text, TextProps } from 'react-native';
import { useLanguage } from '../../contexts/LanguageContext';
import { ThemedText } from './ThemedText';

interface TranslatedTextProps extends TextProps {
  translationKey: string;
  style?: any;
}

export function TranslatedText({ translationKey, style, ...props }: TranslatedTextProps) {
  const { t } = useLanguage();
  
  return (
    <ThemedText 
      style={style}
      {...props}
    >
      {t(translationKey)}
    </ThemedText>
  );
}