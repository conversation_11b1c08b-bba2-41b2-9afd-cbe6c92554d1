import { Ionicons } from '@expo/vector-icons';
import { SymbolView, SymbolViewProps, SymbolWeight } from 'expo-symbols';
import { StyleProp, TextStyle, ViewStyle } from 'react-native';

// Map of Ionicons names to SF Symbol names for iOS
const ioniconsToSFSymbolMap: Record<string, string> = {
  // Payment app icons
  'logo-google': 'g.circle.fill',
  'wallet-outline': 'wallet.pass.fill',
  'phone-portrait-outline': 'iphone',
  'cash-outline': 'indianrupeesign.circle.fill',
  
  // Add more mappings as needed
};

export function IconSymbol({
  name,
  size = 24,
  color,
  style,
  weight = 'regular',
}: {
  name: string | SymbolViewProps['name'];
  size?: number;
  color: string;
  style?: StyleProp<ViewStyle>;
  weight?: SymbolWeight;
}) {
  // Check if this is an Ionicons name that needs special handling
  if (ioniconsToSFSymbolMap[name as string]) {
    const sfSymbolName = ioniconsToSFSymbolMap[name as string];
    
    return (
      <SymbolView
        weight={weight}
        tintColor={color}
        resizeMode="scaleAspectFit"
        name={sfSymbolName as any}
        style={[
          {
            width: size,
            height: size,
          },
          style,
        ]}
      />
    );
  }
  
  // For Ionicons that don't have SF Symbol equivalents, use Ionicons directly
  if (name === 'logo-google' || 
      name === 'wallet-outline' || 
      name === 'phone-portrait-outline' || 
      name === 'cash-outline') {
    // Convert StyleProp<ViewStyle> to ViewStyle | TextStyle | undefined
    const ioniconsStyle = style as ViewStyle | TextStyle | undefined;
    
    return (
      <Ionicons
        name={name as any}
        size={size}
        color={color}
        style={ioniconsStyle}
      />
    );
  }
  
  // Default to using SF Symbols
  return (
    <SymbolView
      weight={weight}
      tintColor={color}
      resizeMode="scaleAspectFit"
      name={name as any}
      style={[
        {
          width: size,
          height: size,
        },
        style,
      ]}
    />
  );
}





