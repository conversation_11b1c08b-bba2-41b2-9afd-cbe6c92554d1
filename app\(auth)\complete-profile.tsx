import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { useRouter } from 'expo-router';
import { useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    Image,
    Keyboard,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    TouchableWithoutFeedback,
    View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../../context/AuthContext';
import { supabase } from '../../lib/supabase';

// Gender options
const genderOptions = [
  { label: 'Male', value: 'male' },
  { label: 'Female', value: 'female' },
  { label: 'Non-binary', value: 'non-binary' },
  { label: 'Prefer not to say', value: 'not-specified' }
];

// Preference options
const preferenceOptions = [
  { label: 'Men', value: 'men' },
  { label: 'Women', value: 'women' },
  { label: 'Everyone', value: 'everyone' }
];

export default function CompleteProfileScreen() {
  const { user, updateUserProfile } = useAuth();
  const router = useRouter();

  // State for form fields
  const [name, setName] = useState(user?.displayName || '');
  const [email, setEmail] = useState(user?.email || '');
  const [bio, setBio] = useState('');
  const [gender, setGender] = useState('');
  const [preference, setPreference] = useState('');
  const [age, setAge] = useState('');
  const [location, setLocation] = useState('');
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState(1); // For multi-step form
  const [photos, setPhotos] = useState<string[]>([]);

  // Additional profile details
  const [dateOfBirth, setDateOfBirth] = useState<Date | null>(null);
  const [dateString, setDateString] = useState('');
  const [height, setHeight] = useState('');
  const [occupation, setOccupation] = useState('');
  const [education, setEducation] = useState('');
  const [languages, setLanguages] = useState<string[]>([]);
  const [newLanguage, setNewLanguage] = useState('');
  const [smoking, setSmoking] = useState('');
  const [drinking, setDrinking] = useState('');
  const [religion, setReligion] = useState('');
  const [interests, setInterests] = useState<string[]>([]);
  const [newInterest, setNewInterest] = useState('');

  // Format date for display
  const formatDate = (date: Date | null) => {
    if (!date) return '';
    return `${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`;
  };

  // Calculate age from DOB
  const calculateAge = (dob: Date | null) => {
    if (!dob) return null;
    const today = new Date();
    let age = today.getFullYear() - dob.getFullYear();
    const monthDiff = today.getMonth() - dob.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
      age--;
    }
    return age;
  };

  // Function to add a new interest/hobby
  const addInterest = () => {
    if (newInterest.trim() && !interests.includes(newInterest.trim())) {
      setInterests([...interests, newInterest.trim()]);
      setNewInterest('');
    }
  };

  // Function to remove an interest/hobby
  const removeInterest = (index: number) => {
    const updatedInterests = [...interests];
    updatedInterests.splice(index, 1);
    setInterests(updatedInterests);
  };

  // Function to add a new language
  const addLanguage = () => {
    if (newLanguage.trim() && !languages.includes(newLanguage.trim())) {
      setLanguages([...languages, newLanguage.trim()]);
      setNewLanguage('');
    }
  };

  // Function to remove a language
  const removeLanguage = (index: number) => {
    const updatedLanguages = [...languages];
    updatedLanguages.splice(index, 1);
    setLanguages(updatedLanguages);
  };

  // Function to handle date input with automatic formatting (MM/DD/YYYY)
  const handleDateChange = (text: string) => {
    // Remove any non-digit characters
    let cleaned = text.replace(/\D/g, '');

    // Add slashes automatically
    let formatted = '';
    if (cleaned.length > 0) {
      // First group - month (MM)
      formatted = cleaned.substring(0, Math.min(2, cleaned.length));

      // Add slash and second group - day (DD)
      if (cleaned.length > 2) {
        formatted += '/' + cleaned.substring(2, Math.min(4, cleaned.length));
      }

      // Add slash and third group - year (YYYY)
      if (cleaned.length > 4) {
        formatted += '/' + cleaned.substring(4, Math.min(8, cleaned.length));
      }
    }

    setDateString(formatted);

    // Parse the date if we have a complete date (MM/DD/YYYY)
    if (formatted.length === 10) {
      const [month, day, year] = formatted.split('/').map(Number);

      // Check if it's a valid date
      if (month >= 1 && month <= 12 && day >= 1 && day <= 31 && year >= 1940) {
        const date = new Date(year, month - 1, day);

        // Verify the date is valid (e.g., not Feb 30)
        if (date.getMonth() === month - 1 && date.getDate() === day && date.getFullYear() === year) {
          setDateOfBirth(date);

          // Update age field based on date of birth
          const calculatedAge = calculateAge(date);
          if (calculatedAge !== null) {
            setAge(calculatedAge.toString());
          }
        }
      }
    } else {
      // Clear the date if incomplete
      setDateOfBirth(null);
    }
  };

  // Function to pick profile image from gallery or camera
  const pickImage = async (useCamera = false) => {
    if (useCamera) {
      // Request camera permissions
      const { status } = await ImagePicker.requestCameraPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Please grant permission to use your camera');
        return;
      }

      // Launch camera
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setProfileImage(result.assets[0].uri);
      }
    } else {
      // Request gallery permissions
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Please grant permission to access your photos');
        return;
      }

      // Launch gallery
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setProfileImage(result.assets[0].uri);
      }
    }
  };

  // Function to show photo source options
  const showImageSourceOptions = () => {
    Alert.alert(
      'Add Profile Photo',
      'Choose a source',
      [
        { text: 'Take Photo', onPress: () => pickImage(true) },
        { text: 'Choose from Gallery', onPress: () => pickImage(false) },
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  };

  // Function to add photos from gallery or camera
  const addPhoto = async (useCamera = false) => {
    if (useCamera) {
      // Request camera permissions
      const { status } = await ImagePicker.requestCameraPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Please grant permission to use your camera');
        return;
      }

      // Launch camera
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 5], // Portrait aspect ratio for dating app
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setPhotos([...photos, result.assets[0].uri]);
      }
    } else {
      // Request gallery permissions
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Please grant permission to access your photos');
        return;
      }

      // Launch gallery
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 5], // Portrait aspect ratio for dating app
        quality: 0.8,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setPhotos([...photos, result.assets[0].uri]);
      }
    }
  };

  // Function to show photo source options for additional photos
  const showAddPhotoOptions = () => {
    Alert.alert(
      'Add Photo',
      'Choose a source',
      [
        { text: 'Take Photo', onPress: () => addPhoto(true) },
        { text: 'Choose from Gallery', onPress: () => addPhoto(false) },
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  };

  // Function to handle form submission
  const handleSubmit = async () => {
    if (step < 5) {
      // Move to next step
      setStep(step + 1);
      return;
    }

    // Validate required fields for final step
    if (!name) {
      Alert.alert('Missing Information', 'Please provide your name');
      setStep(1);
      return;
    }

    if (!gender || !preference) {
      Alert.alert('Missing Information', 'Please select your gender and preference');
      setStep(2);
      return;
    }

    // Make sure we have either age or date of birth
    if (!age && !dateOfBirth) {
      Alert.alert('Missing Information', 'Please provide your age or date of birth');
      setStep(1);
      return;
    }

    // Make sure we have a profile photo
    if (!profileImage && photos.length === 0) {
      Alert.alert('Missing Photo', 'Please add at least one photo to your profile');
      return;
    }

    try {
      setIsLoading(true);

      // Try to upload images to Supabase Storage, but fall back to local URIs if it fails
      let avatarUrl = '';
      let photoUrls = [];

      try {
        // Try to upload profile image if selected
        if (profileImage) {
          const fileExt = profileImage.split('.').pop();
          const fileName = `${user?.id}/avatar.${fileExt}`;

          // Convert image to blob
          const response = await fetch(profileImage);
          const blob = await response.blob();

          // Try to upload to Supabase Storage
          try {
            const { data: uploadData, error: uploadError } = await supabase.storage
              .from('avatars')
              .upload(fileName, blob, { upsert: true });

            if (uploadError) {
              console.error('Error uploading avatar:', uploadError);
              // Fall back to local URI
              avatarUrl = profileImage;
            } else {
              // Get public URL
              const { data: publicUrlData } = await supabase.storage
                .from('avatars')
                .getPublicUrl(fileName);

              avatarUrl = publicUrlData?.publicUrl || profileImage;
            }
          } catch (storageError) {
            console.error('Storage error:', storageError);
            // Fall back to local URI
            avatarUrl = profileImage;
          }
        }

        // Try to upload additional photos
        photoUrls = [...photos]; // Start with local URIs as fallback

        for (let i = 0; i < photos.length; i++) {
          try {
            const photo = photos[i];
            const fileExt = photo.split('.').pop();
            const fileName = `${user?.id}/${Date.now()}-${i}.${fileExt}`;

            // Convert image to blob
            const response = await fetch(photo);
            const blob = await response.blob();

            // Try to upload to Supabase Storage
            const { data: uploadData, error: uploadError } = await supabase.storage
              .from('photos')
              .upload(fileName, blob, { upsert: true });

            if (uploadError) {
              console.error(`Error uploading photo ${i}:`, uploadError);
              // Keep the local URI (already in photoUrls)
            } else {
              // Get public URL
              const { data: publicUrlData } = await supabase.storage
                .from('photos')
                .getPublicUrl(fileName);

              if (publicUrlData?.publicUrl) {
                // Replace the local URI with the public URL
                photoUrls[i] = publicUrlData.publicUrl;
              }
            }
          } catch (photoError) {
            console.error(`Error processing photo ${i}:`, photoError);
            // Keep the local URI (already in photoUrls)
          }
        }
      } catch (imageProcessingError) {
        console.error('Error in image processing:', imageProcessingError);
        // Fall back to local URIs
        avatarUrl = profileImage || '';
        photoUrls = [...photos];
      }

      // Update user profile
      if (user?.id) {
        // Calculate age from DOB if available
        const calculatedAge = calculateAge(dateOfBirth);
        const ageValue = calculatedAge !== null ? calculatedAge : parseInt(age) || null;

        // Create a base update object with fields we know exist
        const baseUpdate = {
          display_name: name,
          email: email,
          bio: bio,
          gender: gender,
          preference: preference,
          age: ageValue,
          avatar_url: avatarUrl,
          photos: photoUrls,
          interests: interests,
          updated_at: new Date().toISOString()
        };

        // Try to update with all fields first
        let { error: profileError } = await supabase
          .from('profiles')
          .update({
            ...baseUpdate,
            // Additional fields that might not exist yet
            date_of_birth: dateOfBirth ? dateOfBirth.toISOString() : null,
            height: height,
            occupation: occupation,
            education: education,
            languages: languages,
            smoking: smoking,
            drinking: drinking,
            religion: religion
          })
          .eq('id', user.id);

        // If we get an error about missing columns, try with just the base fields
        if (profileError && profileError.code === 'PGRST204') {
          console.log('Falling back to base profile update without extended fields');
          const { error: baseError } = await supabase
            .from('profiles')
            .update(baseUpdate)
            .eq('id', user.id);

          profileError = baseError;
        }

        if (profileError) {
          console.error('Error updating profile:', profileError);
          Alert.alert('Error', 'Failed to update profile. Please try again.');
          setIsLoading(false);
          return;
        }

        // Check if discovery entry already exists
        const { data: existingDiscovery, error: checkError } = await supabase
          .from('discoveries')
          .select('id')
          .eq('user_id', user.id)
          .maybeSingle();

        if (checkError) {
          console.error('Error checking for existing discovery:', checkError);
        }

        // Create base discovery data with fields we know exist
        const baseDiscoveryData = {
          user_id: user.id,
          caption: bio,
          imageurl: photoUrls[0] || avatarUrl,
          gender: gender,
          preference: preference,
          updated_at: new Date().toISOString()
        };

        // Extended discovery data with fields that might not exist
        const extendedDiscoveryData = {
          ...baseDiscoveryData,
          age: ageValue,
          location: location,
          interests: interests,
          occupation: occupation,
          religion: religion
        };

        if (existingDiscovery) {
          // Try to update with extended fields first
          let { error: updateError } = await supabase
            .from('discoveries')
            .update(extendedDiscoveryData)
            .eq('user_id', user.id);

          // If we get an error, try with just the base fields
          if (updateError) {
            console.error('Error updating discovery with extended fields:', updateError);
            console.log('Falling back to base discovery update');

            const { error: baseUpdateError } = await supabase
              .from('discoveries')
              .update(baseDiscoveryData)
              .eq('user_id', user.id);

            if (baseUpdateError) {
              console.error('Error updating discovery with base fields:', baseUpdateError);
            }
          }
        } else {
          // Try to insert with extended fields first
          let { error: insertError } = await supabase
            .from('discoveries')
            .insert({
              ...extendedDiscoveryData,
              created_at: new Date().toISOString()
            });

          // If we get an error, try with just the base fields
          if (insertError) {
            console.error('Error creating discovery with extended fields:', insertError);
            console.log('Falling back to base discovery insert');

            const { error: baseInsertError } = await supabase
              .from('discoveries')
              .insert({
                ...baseDiscoveryData,
                created_at: new Date().toISOString()
              });

            if (baseInsertError) {
              console.error('Error creating discovery with base fields:', baseInsertError);
            }
          }
        }



        // Update local user state
        await updateUserProfile({
          displayName: name,
          photoURL: avatarUrl,
          bio: bio,
          gender: gender,
          preference: preference,
          photos: photoUrls,
          // Additional fields
          dateOfBirth: dateOfBirth,
          age: ageValue,
          height: height,
          occupation: occupation,
          education: education,
          languages: languages,
          smoking: smoking,
          drinking: drinking,
          religion: religion,
          interests: interests,
          location: location
        });

        Alert.alert(
          'Profile Completed!',
          'Your profile has been set up successfully.',
          [
            {
              text: 'Start Exploring',
              onPress: () => router.push('/(tabs)')
            }
          ]
        );
      }
    } catch (error) {
      console.error('Error completing profile:', error);
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Render step 1 - Basic Info
  const renderStep1 = () => (
    <View style={styles.formContainer}>
      <Text style={styles.stepTitle}>Basic Information</Text>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Full Name</Text>
        <TextInput
          style={styles.input}
          value={name}
          onChangeText={setName}
          placeholder="Your name"
        />
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Email</Text>
        <TextInput
          style={styles.input}
          value={email}
          onChangeText={setEmail}
          placeholder="Your email"
          keyboardType="email-address"
          autoCapitalize="none"
        />
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Date of Birth (MM/DD/YYYY)</Text>
        <TextInput
          style={styles.input}
          value={dateString}
          onChangeText={handleDateChange}
          placeholder="MM/DD/YYYY"
          keyboardType="number-pad"
          maxLength={10}
        />

        {dateOfBirth && (
          <Text style={styles.helperText}>
            Age: {calculateAge(dateOfBirth)} years
          </Text>
        )}

        <Text style={styles.helperText}>
          Enter your date of birth in MM/DD/YYYY format.
          Slashes will be added automatically as you type.
        </Text>
      </View>

      {!dateOfBirth && (
        <View style={styles.formGroup}>
          <Text style={styles.label}>Age</Text>
          <TextInput
            style={styles.input}
            value={age}
            onChangeText={setAge}
            placeholder="Your age"
            keyboardType="number-pad"
          />
        </View>
      )}

      <View style={styles.formGroup}>
        <Text style={styles.label}>Location</Text>
        <TextInput
          style={styles.input}
          value={location}
          onChangeText={setLocation}
          placeholder="Your location"
        />
      </View>
    </View>
  );

  // Render step 2 - Preferences
  const renderStep2 = () => (
    <View style={styles.formContainer}>
      <Text style={styles.stepTitle}>Your Preferences</Text>

      <View style={styles.formGroup}>
        <Text style={styles.label}>About Me</Text>
        <TextInput
          style={[styles.input, styles.textArea]}
          value={bio}
          onChangeText={setBio}
          placeholder="Tell us about yourself..."
          multiline
          numberOfLines={4}
        />
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.label}>I am a</Text>
        <View style={styles.optionsContainer}>
          {genderOptions.map((option) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.optionButton,
                gender === option.value && styles.selectedOption
              ]}
              onPress={() => setGender(option.value)}
            >
              <Text style={[
                styles.optionText,
                gender === option.value && styles.selectedOptionText
              ]}>
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.label}>I am interested in</Text>
        <View style={styles.optionsContainer}>
          {preferenceOptions.map((option) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.optionButton,
                preference === option.value && styles.selectedOption
              ]}
              onPress={() => setPreference(option.value)}
            >
              <Text style={[
                styles.optionText,
                preference === option.value && styles.selectedOptionText
              ]}>
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Height</Text>
        <TextInput
          style={styles.input}
          value={height}
          onChangeText={setHeight}
          placeholder="Your height (e.g., 5'10 or 178cm)"
        />
      </View>
    </View>
  );

  // Render step 3 - Career & Lifestyle
  const renderStep3 = () => (
    <View style={styles.formContainer}>
      <Text style={styles.stepTitle}>Career & Lifestyle</Text>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Occupation</Text>
        <TextInput
          style={styles.input}
          value={occupation}
          onChangeText={setOccupation}
          placeholder="What do you do?"
        />
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Education</Text>
        <TextInput
          style={styles.input}
          value={education}
          onChangeText={setEducation}
          placeholder="Where did you study?"
        />
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Smoking</Text>
        <View style={styles.optionsContainer}>
          {['Non-smoker', 'Social smoker', 'Regular smoker'].map((option) => (
            <TouchableOpacity
              key={option}
              style={[
                styles.optionButton,
                smoking === option && styles.selectedOption
              ]}
              onPress={() => setSmoking(option)}
            >
              <Text style={[
                styles.optionText,
                smoking === option && styles.selectedOptionText
              ]}>
                {option}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Drinking</Text>
        <View style={styles.optionsContainer}>
          {['Non-drinker', 'Social drinker', 'Regular drinker'].map((option) => (
            <TouchableOpacity
              key={option}
              style={[
                styles.optionButton,
                drinking === option && styles.selectedOption
              ]}
              onPress={() => setDrinking(option)}
            >
              <Text style={[
                styles.optionText,
                drinking === option && styles.selectedOptionText
              ]}>
                {option}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Religion/Spirituality</Text>
        <TextInput
          style={styles.input}
          value={religion}
          onChangeText={setReligion}
          placeholder="Your religious or spiritual beliefs"
        />
      </View>
    </View>
  );

  // Render step 4 - Languages & Interests
  const renderStep4 = () => (
    <View style={styles.formContainer}>
      <Text style={styles.stepTitle}>Languages & Interests</Text>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Languages</Text>
        <Text style={styles.helperText}>
          Add languages you speak
        </Text>

        <View style={styles.interestInputContainer}>
          <TextInput
            style={styles.interestInput}
            value={newLanguage}
            onChangeText={setNewLanguage}
            placeholder="Add a language..."
            onSubmitEditing={addLanguage}
          />
          <TouchableOpacity
            style={styles.addInterestButton}
            onPress={addLanguage}
          >
            <Ionicons name="add" size={24} color="#fff" />
          </TouchableOpacity>
        </View>

        <View style={styles.interestTags}>
          {languages.map((language, index) => (
            <View key={index} style={styles.interestTag}>
              <Text style={styles.interestTagText}>{language}</Text>
              <TouchableOpacity
                onPress={() => removeLanguage(index)}
                style={styles.removeInterestButton}
              >
                <Ionicons name="close" size={16} color="#fff" />
              </TouchableOpacity>
            </View>
          ))}
        </View>
      </View>

      <View style={styles.formGroup}>
        <Text style={styles.label}>Interests & Hobbies</Text>
        <Text style={styles.helperText}>
          Add interests to help others connect with you
        </Text>

        <View style={styles.interestInputContainer}>
          <TextInput
            style={styles.interestInput}
            value={newInterest}
            onChangeText={setNewInterest}
            placeholder="Add an interest..."
            onSubmitEditing={addInterest}
          />
          <TouchableOpacity
            style={styles.addInterestButton}
            onPress={addInterest}
          >
            <Ionicons name="add" size={24} color="#fff" />
          </TouchableOpacity>
        </View>

        <View style={styles.interestTags}>
          {interests.map((interest, index) => (
            <View key={index} style={styles.interestTag}>
              <Text style={styles.interestTagText}>{interest}</Text>
              <TouchableOpacity
                onPress={() => removeInterest(index)}
                style={styles.removeInterestButton}
              >
                <Ionicons name="close" size={16} color="#fff" />
              </TouchableOpacity>
            </View>
          ))}
        </View>
      </View>
    </View>
  );

  // Render step 5 - Photos
  const renderStep5 = () => (
    <View style={styles.formContainer}>
      <Text style={styles.stepTitle}>Add Your Photos</Text>

      <View style={styles.profileImageContainer}>
        <TouchableOpacity onPress={showImageSourceOptions}>
          {profileImage ? (
            <Image source={{ uri: profileImage }} style={styles.profileImage} />
          ) : (
            <View style={styles.profileImagePlaceholder}>
              <Ionicons name="camera" size={50} color="#ccc" />
              <Text style={styles.addPhotoText}>Tap to add photo</Text>
            </View>
          )}
        </TouchableOpacity>
        <Text style={styles.profileImageLabel}>Profile Photo</Text>
      </View>

      <Text style={styles.sectionTitle}>Additional Photos</Text>
      <Text style={styles.photoTip}>Add more photos to increase your chances of matching!</Text>

      <View style={styles.photosGrid}>
        {photos.map((photo, index) => (
          <View key={index} style={styles.photoItem}>
            <Image source={{ uri: photo }} style={styles.photoImage} />
          </View>
        ))}

        {photos.length < 5 && (
          <TouchableOpacity style={styles.addPhotoButton} onPress={showAddPhotoOptions}>
            <Ionicons name="camera" size={30} color="#FF4B00" />
            <Text style={styles.addPhotoButtonText}>Add Photo</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  // Dismiss keyboard when tapping outside of inputs
  const dismissKeyboard = () => {
    Keyboard.dismiss();
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <TouchableWithoutFeedback onPress={dismissKeyboard}>
          <ScrollView
            contentContainerStyle={styles.scrollContent}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
          >
            <View style={styles.header}>
              <Text style={styles.title}>Complete Your Profile</Text>
              <Text style={styles.subtitle}>
                Let's set up your profile so others can discover you
              </Text>
            </View>

            <View style={styles.progressContainer}>
              {[1, 2, 3, 4, 5].map((i) => (
                <View
                  key={i}
                  style={[
                    styles.progressDot,
                    i <= step && styles.activeDot
                  ]}
                />
              ))}
            </View>

            {step === 1 && renderStep1()}
            {step === 2 && renderStep2()}
            {step === 3 && renderStep3()}
            {step === 4 && renderStep4()}
            {step === 5 && renderStep5()}

            <View style={styles.buttonContainer}>
              {step > 1 && (
                <TouchableOpacity
                  style={styles.backButton}
                  onPress={() => setStep(step - 1)}
                >
                  <Text style={styles.backButtonText}>Back</Text>
                </TouchableOpacity>
              )}

              <TouchableOpacity
                style={styles.button}
                onPress={handleSubmit}
                disabled={isLoading}
              >
                {isLoading ? (
                  <ActivityIndicator color="#fff" />
                ) : (
                  <Text style={styles.buttonText}>
                    {step < 5 ? 'Next' : 'Complete Profile'}
                  </Text>
                )}
              </TouchableOpacity>
            </View>

            {/* Add extra space at the bottom for keyboard */}
            <View style={styles.bottomSpacer} />
          </ScrollView>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
  },
  scrollContent: {
    padding: 20,
  },
  header: {
    marginBottom: 30,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#AAAAAA',
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 30,
  },
  progressDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#333333',
    marginHorizontal: 5,
  },
  activeDot: {
    backgroundColor: '#FF4B00',
  },
  formContainer: {
    marginBottom: 30,
  },
  bottomSpacer: {
    height: 40, // Extra space at the bottom
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 20,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 8,
  },
  helperText: {
    fontSize: 14,
    color: '#AAAAAA',
    marginBottom: 12,
  },
  input: {
    backgroundColor: '#1A1A1A',
    borderRadius: 8,
    padding: 12,
    color: '#FFFFFF',
    fontSize: 16,
  },
  inputText: {
    color: '#FFFFFF',
    fontSize: 16,
  },
  placeholderText: {
    color: '#777777',
    fontSize: 16,
  },
  interestInputContainer: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  interestInput: {
    flex: 1,
    backgroundColor: '#1A1A1A',
    borderRadius: 8,
    padding: 12,
    color: '#FFFFFF',
    fontSize: 16,
    marginRight: 8,
  },
  addInterestButton: {
    backgroundColor: '#FF4B00',
    borderRadius: 8,
    width: 48,
    justifyContent: 'center',
    alignItems: 'center',
  },
  interestTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  interestTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FF4B00',
    borderRadius: 20,
    paddingVertical: 6,
    paddingHorizontal: 12,
    marginRight: 8,
    marginBottom: 8,
  },
  interestTagText: {
    color: '#FFFFFF',
    marginRight: 4,
  },
  removeInterestButton: {
    width: 16,
    height: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  optionButton: {
    backgroundColor: '#1A1A1A',
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 16,
    marginRight: 10,
    marginBottom: 10,
  },
  selectedOption: {
    backgroundColor: '#FF4B00',
  },
  optionText: {
    color: '#FFFFFF',
    fontSize: 14,
  },
  selectedOptionText: {
    fontWeight: 'bold',
  },
  profileImageContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  profileImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  profileImagePlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#1A1A1A',
    justifyContent: 'center',
    alignItems: 'center',
  },
  addPhotoText: {
    color: '#AAAAAA',
    fontSize: 12,
    marginTop: 8,
    textAlign: 'center',
  },
  profileImageLabel: {
    color: '#FFFFFF',
    marginTop: 8,
    fontSize: 14,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  photoTip: {
    color: '#AAAAAA',
    fontSize: 14,
    marginBottom: 16,
  },
  photosGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  photoItem: {
    width: '30%',
    aspectRatio: 0.8,
    marginRight: '3%',
    marginBottom: 10,
    borderRadius: 8,
    overflow: 'hidden',
  },
  photoImage: {
    width: '100%',
    height: '100%',
  },
  addPhotoButton: {
    width: '30%',
    aspectRatio: 0.8,
    backgroundColor: '#1A1A1A',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: '3%',
    marginBottom: 10,
  },
  addPhotoButtonText: {
    color: '#FF4B00',
    fontSize: 12,
    marginTop: 5,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  button: {
    backgroundColor: '#FF4B00',
    borderRadius: 8,
    paddingVertical: 14,
    paddingHorizontal: 20,
    flex: 1,
    alignItems: 'center',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  backButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#333333',
    borderRadius: 8,
    paddingVertical: 14,
    paddingHorizontal: 20,
    marginRight: 10,
  },
  backButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
  },
});
