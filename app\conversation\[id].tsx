import { Ionicons } from '@expo/vector-icons';
import * as Camera from 'expo-camera';
import * as FileSystem from 'expo-file-system';
import * as ImagePicker from 'expo-image-picker';
import * as Location from 'expo-location';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useEffect, useMemo, useRef, useState } from 'react';
import {
  Alert,
  FlatList,
  Image,
  KeyboardAvoidingView,
  Linking,
  Modal,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  useColorScheme,
  View
} from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { useAuth } from '../../context/AuthContext';
import { supabase } from '../../lib/supabase';
import { Message } from '../../services/MessageService';
import { getUserCoinBalance } from '../../services/paymentService';
// import { startVideoCall, useVideoCall } from '../../services/VideoCallService';
// import { startVoiceCall, useVoiceCall } from '../../services/VoiceCallService';
// import { VoiceCallUI } from '../../components/VoiceCallUI';

// Extended Message interface to include additional properties
interface ExtendedMessage extends Message {
  replyTo?: Message;
  media?: {
    type: string;
    uri: string;
  };
  metadata?: {
    type: string;
    latitude?: number;
    longitude?: number;
    locationName?: string;
    amount?: number;
    action?: string;
  };
  avatar?: string;
  type?: string;
}

export default function ConversationScreen() {
  const router = useRouter();
  const params = useLocalSearchParams<{ id: string }>();
  const conversationId = params.id;
  const colorScheme = useColorScheme();
  const insets = useSafeAreaInsets();
  const { user } = useAuth();
  
  // Refs
  const inputRef = useRef<TextInput>(null);
  const flatListRef = useRef<FlatList>(null);
  
  // State variables
  const [messages, setMessages] = useState<ExtendedMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [replyingTo, setReplyingTo] = useState<ExtendedMessage | null>(null);
  const [coinModalVisible, setCoinModalVisible] = useState(false);
  const [coinAmount, setCoinAmount] = useState('100');
  const [coinBalance, setCoinBalance] = useState(0);
  const [attachmentModalVisible, setAttachmentModalVisible] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [isTyping, setIsTyping] = useState(false);
  const [cameraPermission, setCameraPermission] = useState(false);
  const [loading, setLoading] = useState(false);
  
  // State for contact info
  const [contactInfo, setContactInfo] = useState({
    id: conversationId,
    name: 'Loading...',
    avatar: 'https://via.placeholder.com/150',
    isOnline: false
  });
  
  // Remove these state variables
  // const [videoCallVisible, setVideoCallVisible] = useState(false);
  // const [voiceCallVisible, setVoiceCallVisible] = useState(false);

  // Remove these hooks
  // const videoCall = useVideoCall(contactInfo.id, contactInfo.name, contactInfo.avatar);
  // const voiceCall = useVoiceCall(contactInfo.id, contactInfo.name, contactInfo.avatar);

  // Handle starting calls
  /*
  const handleStartVideoCall = async () => {
    try {
      // Check if user exists before proceeding
      if (!user) {
        console.error('User is not logged in');
        Alert.alert('Error', 'You need to be logged in to make calls');
        return;
      }

      // Create a call record in Supabase
      const { data, error } = await supabase
        .from('calls')
        .insert({
          caller_id: user.id,
          receiver_id: contactInfo.id,
          call_type: 'video',
          status: 'initiated',
          conversation_id: conversationId
        })
        .select()
        .single();
        
      if (error) {
        console.error('Error creating call record:', error);
        return;
      }
      
      // Start the actual video call
      const callStarted = await startVideoCall(contactInfo.id);
      if (callStarted) {
        setVideoCallVisible(true);
        
        // Update call status in real-time
        await supabase
          .from('calls')
          .update({ status: 'connected' })
          .eq('id', data.id);
      }
    } catch (error) {
      console.error('Error starting video call:', error);
      Alert.alert('Call Error', 'Could not start video call. Please try again.');
    }
  };

  const handleStartVoiceCall = async () => {
    try {
      console.log('Voice call button pressed');
      
      // Check if user exists before proceeding
      if (!user) {
        console.error('User is not logged in');
        Alert.alert('Error', 'You need to be logged in to make calls');
        return;
      }

      console.log('Creating call record in Supabase');
      // Create a call record in Supabase
      const { data, error } = await supabase
        .from('calls')
        .insert({
          caller_id: user.id,
          receiver_id: contactInfo.id,
          call_type: 'voice',
          status: 'initiated',
          conversation_id: conversationId
        })
        .select()
        .single();
        
      if (error) {
        console.error('Error creating call record:', error);
        Alert.alert('Error', 'Could not create call record. Please try again.');
        return;
      }
      
      console.log('Call record created:', data);
      console.log('Starting voice call service');
      
      // Start the actual voice call
      const callStarted = await startVoiceCall(contactInfo.id);
      console.log('Voice call started:', callStarted);
      
      if (callStarted) {
        console.log('Setting voice call visible to true');
        setVoiceCallVisible(true);
        
        console.log('Updating call status to connected');
        // Update call status in real-time
        await supabase
          .from('calls')
          .update({ status: 'connected' })
          .eq('id', data.id);
      } else {
        console.log('Call failed to start');
        Alert.alert('Call Error', 'Could not start voice call. Please try again.');
      }
    } catch (error) {
      console.error('Error starting voice call:', error);
      Alert.alert('Call Error', 'Could not start voice call. Please try again.');
    }
  };

  // Handle ending calls
  const handleEndVideoCall = async () => {
    videoCall.endCall();
    setVideoCallVisible(false);
    
    // Update call status in Supabase
    try {
      // Check if user exists before proceeding
      if (!user) {
        console.error('User is not logged in');
        return;
      }

      const { data, error } = await supabase
        .from('calls')
        .update({ status: 'ended', end_time: new Date().toISOString() })
        .eq('caller_id', user.id)
        .eq('receiver_id', contactInfo.id)
        .eq('call_type', 'video')
        .eq('status', 'connected')
        .select()
        .single();
        
      if (error) {
        console.error('Error updating call record:', error);
      }
    } catch (error) {
      console.error('Error ending video call:', error);
    }
  };

  const handleEndVoiceCall = async () => {
    voiceCall.endCall();
    setVoiceCallVisible(false);
    
    // Update call status in Supabase
    try {
      // Check if user exists before proceeding
      if (!user) {
        console.error('User is not logged in');
        return;
      }

      const { data, error } = await supabase
        .from('calls')
        .update({ status: 'ended', end_time: new Date().toISOString() })
        .eq('caller_id', user.id)
        .eq('receiver_id', contactInfo.id)
        .eq('call_type', 'voice')
        .eq('status', 'connected')
        .select()
        .single();
        
      if (error) {
        console.error('Error updating call record:', error);
      }
    } catch (error) {
      console.error('Error ending voice call:', error);
    }
  };
  */
  
  // Create styles using the colorScheme
  const styles = useMemo(() => createStyles(colorScheme === 'dark' ? 'dark' : 'light'), [colorScheme]);
  
  // Helper function to get tint color based on color scheme
  const getTintColor = () => colorScheme === 'dark' ? '#0A84FF' : '#007AFF';
  
  // Fetch user's coin balance
  useEffect(() => {
    const fetchCoinBalance = async () => {
      if (user?.id) {
        try {
          const balance = await getUserCoinBalance(user.id);
          setCoinBalance(balance);
        } catch (error) {
          console.error('Error fetching coin balance:', error);
        }
      }
    };
    
    fetchCoinBalance();
  }, [user]);
  
  // Fetch conversation and contact details
  useEffect(() => {
    const fetchConversationDetails = async () => {
      if (!conversationId || !user?.id) return;
      
      try {
        // Get conversation data
        const { data: conversationData, error: conversationError } = await supabase
          .from('conversations')
          .select('*')
          .eq('id', conversationId)
          .single();
        
        if (conversationError) {
          console.error('Error fetching conversation:', conversationError);
          return;
        }
        
        // Determine the other participant's ID
        const otherParticipantId = conversationData.participant1_id === user.id 
          ? conversationData.participant2_id 
          : conversationData.participant1_id;
        
        // Get the other participant's profile
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', otherParticipantId)
          .single();
        
        if (profileError) {
          console.error('Error fetching profile:', profileError);
          return;
        }
        
        // Get the real avatar URL - check for photos array first, then avatar_url
        let avatarUrl = 'https://via.placeholder.com/150';
        
        if (profileData.photos && profileData.photos.length > 0) {
          // Use the first photo from the photos array if available
          avatarUrl = profileData.photos[0];
        } else if (profileData.avatar_url) {
          // Fall back to avatar_url if available
          avatarUrl = profileData.avatar_url;
        }
        
        // Update contact info with real data
        setContactInfo({
          id: otherParticipantId,
          name: profileData.display_name || profileData.username || 'User',
          avatar: avatarUrl,
          isOnline: profileData.is_online || false
        });
        
        // Fetch messages for this conversation
        fetchMessages();
      } catch (error) {
        console.error('Error in fetchConversationDetails:', error);
      }
    };
    
    fetchConversationDetails();
  }, [conversationId, user?.id]);
  
  // Fetch messages for the conversation
  const fetchMessages = async () => {
    if (!conversationId) return;
    
    try {
      const { data: messagesData, error: messagesError } = await supabase
        .from('messages')
        .select('*')
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: true });
      
      if (messagesError) {
        console.error('Error fetching messages:', messagesError);
        return;
      }
      
      console.log('Fetched messages:', messagesData.length);
      
      // Transform messages to the format expected by the UI
      const transformedMessages = messagesData.map(msg => ({
        id: msg.id,
        text: msg.content,
        senderId: msg.sender_id === user?.id ? 'currentUser' : 'otherUser',
        timestamp: new Date(msg.created_at).getTime(),
        isStarred: msg.is_starred || false,
        type: msg.type || 'text',
        metadata: msg.metadata || undefined,
        media: msg.media_url ? {
          type: msg.media_type || 'image',
          uri: msg.media_url
        } : undefined,
        replyTo: msg.reply_to_id ? {
          id: msg.reply_to_id,
          text: msg.reply_to_text || '',
          senderId: msg.reply_to_sender_id || '',
          timestamp: new Date().getTime() // Use current time since reply_to_timestamp doesn't exist
        } : undefined
      }));
      
      console.log('Transformed messages:', transformedMessages.length);
      setMessages(transformedMessages);
    } catch (error) {
      console.error('Error in fetchMessages:', error);
    }
  };
  
  // Camera permission state
  const [permission, setPermission] = useState<Camera.PermissionResponse | null>(null);
  
  // Check camera permission on mount
  useEffect(() => {
    (async () => {
      // Using the correct Camera API for permissions
      const [cameraPermissionInfo, requestPermission] = Camera.useCameraPermissions();
      
      if (!cameraPermissionInfo?.granted) {
        const permissionResult = await requestPermission();
        setCameraPermission(permissionResult.granted);
      } else {
        setCameraPermission(true);
      }
    })();
  }, []);
  
  // Scroll to end when messages change
  useEffect(() => {
    setTimeout(() => {
      if (flatListRef.current && messages.length > 0) {
        flatListRef.current.scrollToEnd({ animated: true });
      }
    }, 100);
  }, [messages]);
  
  // Set up real-time subscription for new messages
  useEffect(() => {
    if (!conversationId) return;
    
    // Create a channel for real-time updates
    const channel = supabase
      .channel(`public:messages:conversation_id=eq.${conversationId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `conversation_id=eq.${conversationId}`
        },
        async (payload) => {
          console.log('New message received:', payload);
          
          // Only add the message if it's not from the current user
          // (to avoid duplicates since we add our own messages immediately)
          if (payload.new && payload.new.sender_id !== user?.id) {
            const newMessage = {
              id: payload.new.id,
              text: payload.new.content,
              senderId: 'otherUser',
              timestamp: new Date(payload.new.created_at).getTime(),
              isStarred: !!payload.new.is_starred,
              type: payload.new.type || 'text',
              metadata: payload.new.metadata || undefined,
              media: payload.new.media_url ? {
                type: payload.new.media_type || 'image',
                uri: payload.new.media_url
              } : undefined,
              replyTo: payload.new.reply_to_id ? {
                id: payload.new.reply_to_id,
                text: payload.new.reply_to_text || '',
                senderId: payload.new.reply_to_sender_id || '',
                timestamp: new Date().getTime()
              } : undefined
            };
            
            setMessages(prevMessages => [...prevMessages, newMessage]);
          }
          
          // Mark message as read in the database
          if (payload.new && payload.new.sender_id !== user?.id) {
            // Update unread count in the conversation
            await supabase
              .from('conversations')
              .update({ unread_count: 0 })
              .eq('id', conversationId);
          }
        }
      );
  
    // Subscribe to the channel
    channel.subscribe((status) => {
      if (status === 'SUBSCRIBED') {
        console.log('Subscribed to new messages');
      }
    });
  
    // Set up real-time subscription for contact's online status
    const statusChannel = supabase
      .channel(`public:user_status:user_id=eq.${contactInfo.id}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_status',
          filter: `user_id=eq.${contactInfo.id}`
        },
        (payload: { new: { is_online?: boolean, last_seen?: string } }) => {
          console.log('Contact status changed:', payload);
          if (payload.new) {
            const isRecentlyActive = payload.new.last_seen ? 
              (new Date().getTime() - new Date(payload.new.last_seen).getTime() < 5 * 60 * 1000) : 
              false;
            
            setContactInfo(prev => ({
              ...prev,
              isOnline: payload.new.is_online || isRecentlyActive
            }));
          }
        }
      )
      .subscribe();
      
    // Clean up both subscriptions when component unmounts
    return () => {
      supabase.removeChannel(channel);
      supabase.removeChannel(statusChannel);
    };
  }, [conversationId, user?.id]);
  
  // Set up polling for contact's online status
  useEffect(() => {
    if (!conversationId || !contactInfo.id) return;
    
    // Initial fetch of online status
    const fetchOnlineStatus = async () => {
      try {
        const { data, error } = await supabase
          .from('user_status')
          .select('is_online, last_seen')
          .eq('user_id', contactInfo.id)
          .single();
        
        if (!error && data) {
          const isRecentlyActive = data.last_seen ? 
            (new Date().getTime() - new Date(data.last_seen).getTime() < 5 * 60 * 1000) : 
            false;
            
          setContactInfo(prev => ({
            ...prev,
            isOnline: data.is_online || isRecentlyActive
          }));
          
          console.log('Contact online status:', data.is_online, 'Last seen:', data.last_seen);
        }
      } catch (error) {
        console.error('Error fetching online status:', error);
      }
    };
    
    // Fetch immediately
    fetchOnlineStatus();
    
    // Then set up polling
    const pollInterval = setInterval(fetchOnlineStatus, 5000); // Poll every 5 seconds
    
    // Clean up polling when component unmounts
    return () => {
      clearInterval(pollInterval);
    };
  }, [conversationId, contactInfo.id]);

  // Update user's own online status when component mounts/unmounts
  useEffect(() => {
    if (!user?.id) return;
    
    // Set user as online when component mounts
    const updateOnlineStatus = async (status: boolean) => {
      try {
        // First check if a record exists
        const { data, error: checkError } = await supabase
          .from('user_status')
          .select('user_id')
          .eq('user_id', user.id)
          .maybeSingle();
        
        if (checkError) {
          console.error('Error checking user status:', checkError);
          return;
        }
        
        if (data) {
          // Update existing record
          const { error } = await supabase
            .from('user_status')
            .update({ 
              is_online: status,
              last_seen: new Date().toISOString()
            })
            .eq('user_id', user.id);
          
          if (error) {
            console.error('Error updating online status:', error);
          }
        } else {
          // Insert new record
          const { error } = await supabase
            .from('user_status')
            .insert({ 
              user_id: user.id,
              is_online: status,
              last_seen: new Date().toISOString()
            });
          
          if (error) {
            console.error('Error inserting online status:', error);
          }
        }
      } catch (error) {
        console.error('Exception in updateOnlineStatus:', error);
      }
    };
    
    // Set user as online when conversation screen opens
    updateOnlineStatus(true);
    
    // Set up interval to refresh online status every minute
    const intervalId = setInterval(() => {
      updateOnlineStatus(true);
    }, 60000);
    
    // Set user as offline when component unmounts
    return () => {
      clearInterval(intervalId);
      updateOnlineStatus(false);
    };
  }, [user?.id]);
  
  // Handlers
  const handleTyping = () => {
    setIsTyping(true);
    
    // Clear typing indicator after 2 seconds of inactivity
    setTimeout(() => {
      setIsTyping(false);
    }, 2000);
  };
  
  const handleSendMessage = () => {
    if (newMessage.trim()) {
      sendTextMessage(newMessage);
      setNewMessage('');
    }
  };
  
  const sendTextMessage = async (text: string) => {
    if (!text.trim() || !user?.id || !conversationId) return;
    
    try {
      // Define the message data interface with all possible fields
      interface MessageData {
        conversation_id: string;
        sender_id: string;
        content: string;
        created_at: string;
        reply_to_id?: string;
        reply_to_text?: string;
        reply_to_sender_id?: string;
      }
      
      // Create message object with only the fields that exist in the database
      const messageData: MessageData = {
        conversation_id: conversationId,
        sender_id: user.id,
        content: text.trim(),
        created_at: new Date().toISOString()
      };
      
      // Only add reply fields if replying to a message
      if (replyingTo?.id) {
        messageData.reply_to_id = replyingTo.id;
        messageData.reply_to_text = replyingTo.text;
        messageData.reply_to_sender_id = replyingTo.senderId;
      }
      
      console.log('Sending message:', messageData);
      
      // Create a temporary message ID
      const tempId = `temp_${Date.now()}`;
      
      // Add message to local state immediately for instant feedback
      const tempMsg = {
        id: tempId,
        text: text.trim(),
        senderId: 'currentUser',
        timestamp: Date.now(),
        isStarred: false,
        type: 'text',
        replyTo: replyingTo || undefined,
        isSending: true // Add a flag to indicate the message is being sent
      };
      
      setMessages(prevMessages => [...prevMessages, tempMsg]);
      setReplyingTo(null);
      setNewMessage('');
      
      // Focus input after sending
      if (inputRef.current) {
        inputRef.current.focus();
      }
      
      // Insert message into database
      const { data: newMessage, error: insertError } = await supabase
        .from('messages')
        .insert(messageData)
        .select()
        .single();
      
      if (insertError) {
        console.error('Error sending message:', insertError);
        
        // Update the temporary message to show error
        setMessages(prevMessages => 
          prevMessages.map(msg => 
            msg.id === tempId 
              ? { ...msg, error: true, isSending: false } 
              : msg
          )
        );
        return;
      }
      
      console.log('Message sent successfully:', newMessage);
      
      // Update conversation's last message and timestamp
      await supabase
        .from('conversations')
        .update({
          last_message: text.trim(),
          updated_at: new Date().toISOString()
        })
        .eq('id', conversationId);
      
      // Replace the temporary message with the real one
      setMessages(prevMessages => 
        prevMessages.map(msg => 
          msg.id === tempId 
            ? {
                id: newMessage.id,
                text: text.trim(),
                senderId: 'currentUser',
                timestamp: new Date(newMessage.created_at).getTime(),
                isStarred: false,
                type: 'text',
                replyTo: replyingTo || undefined,
                isSending: false
              } 
            : msg
        )
      );
    } catch (error) {
      console.error('Error in sendTextMessage:', error);
    }
  };
  
  const sendImageMessage = async (imageUri: string) => {
    if (!user?.id || !conversationId) return;
    
    try {
      // For Expo, we need to read the file first
      const fileInfo = await FileSystem.getInfoAsync(imageUri);
      
      if (!fileInfo.exists) {
        console.error('File does not exist');
        return;
      }
      
      // Read the file as base64
      const base64 = await FileSystem.readAsStringAsync(imageUri, {
        encoding: FileSystem.EncodingType.Base64,
      });
      
      // Convert base64 to blob
      const blob = await fetch(`data:image/jpeg;base64,${base64}`).then(res => res.blob());
      
      // Upload blob to storage
      const fileName = `${user.id}/${Date.now()}.jpg`;
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('message_media')
        .upload(fileName, blob);
      
      if (uploadError) {
        console.error('Error uploading image:', uploadError);
        return;
      }
      
      // Get public URL
      const { data: publicUrlData } = await supabase.storage
        .from('message_media')
        .getPublicUrl(fileName);
      
      const publicUrl = publicUrlData?.publicUrl;
      
      // Create message in database
      const messageData = {
        conversation_id: conversationId,
        sender_id: user.id,
        content: '📷 Image',
        type: 'image',
        created_at: new Date().toISOString(),
        media_url: publicUrl,
        media_type: 'image'
      };
      
      const { data: newMessage, error: insertError } = await supabase
        .from('messages')
        .insert(messageData)
        .select()
        .single();
      
      if (insertError) {
        console.error('Error sending image message:', insertError);
        return;
      }
      
      // Update conversation
      await supabase
        .from('conversations')
        .update({
          last_message: '📷 Image',
          updated_at: new Date().toISOString()
        })
        .eq('id', conversationId);
      
      // Add to local state
      const newMsg = {
        id: newMessage.id,
        text: '📷 Image',
        senderId: 'currentUser',
        timestamp: new Date(newMessage.created_at).getTime(),
        isStarred: false,
        type: 'image',
        media: {
          type: 'image',
          uri: publicUrl
        }
      };
      
      setMessages(prevMessages => [...prevMessages, newMsg]);
    } catch (error) {
      console.error('Error in sendImageMessage:', error);
      Alert.alert('Error', 'Failed to send image. Please try again.');
    }
  };
  
  const handleSendLocationMessage = async (latitude: number, longitude: number, name?: string) => {
    try {
      // Create a location message
      const locationText = `📍 Location: ${name || 'Shared Location'}`;
      
      // Create a new message object
      const newMsg: ExtendedMessage = {
        id: `msg_${Date.now()}`,
        text: locationText,
        senderId: 'currentUser',
        timestamp: Date.now(),
        isStarred: false,
        metadata: {
          type: 'location',
          latitude,
          longitude,
          locationName: name
        }
      };
      
      // Add the message to the conversation
      setMessages(prevMessages => [...prevMessages, newMsg]);
      
      return true;
    } catch (error) {
      console.error('Error sending location:', error);
      Alert.alert('Error', 'Failed to send location. Please try again.');
      return false;
    }
  };
  
  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert(
          'Location Permission Required',
          'Please allow access to your location to share it.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Settings', onPress: () => Linking.openSettings() }
          ]
        );
        return null;
      }
      
      const location = await Location.getCurrentPositionAsync({});
      
      // Get location name (reverse geocoding)
      const [address] = await Location.reverseGeocodeAsync({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude
      });
      
      const locationName = address 
        ? `${address.street || ''}, ${address.city || ''}`
        : 'Current Location';
      
      return {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        name: locationName
      };
    } catch (error) {
      console.error('Error getting location:', error);
      return null;
    }
  };
  
  const takePhoto = async () => {
    if (!cameraPermission) {
      const [permissionResponse, requestPermission] = Camera.useCameraPermissions();
      
      if (!permissionResponse?.granted) {
        const newPermission = await requestPermission();
        
        if (!newPermission.granted) {
          Alert.alert(
            'Camera Permission Required',
            'Please allow access to your camera to take photos.',
            [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Settings', onPress: () => Linking.openSettings() }
            ]
          );
          return;
        }
      }
      setCameraPermission(true);
    }
    
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        sendImageMessage(asset.uri);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
    }
  };
  
  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        sendImageMessage(asset.uri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
    }
  };
  
  const deleteMessage = async (messageId: string) => {
    try {
      const { error } = await supabase
        .from('messages')
        .delete()
        .eq('id', messageId);
      
      if (error) {
        console.error('Error deleting message:', error);
        return;
      }
      
      // Remove from local state
      setMessages(prevMessages => prevMessages.filter(msg => msg.id !== messageId));
    } catch (error) {
      console.error('Error in deleteMessage:', error);
    }
  };
  
  const toggleStarMessage = async (messageId: string) => {
    try {
      // Find the message in local state
      const message = messages.find(msg => msg.id === messageId);
      if (!message) return;
      
      // Toggle star status in database
      const { error } = await supabase
        .from('messages')
        .update({ is_starred: !message.isStarred })
        .eq('id', messageId);
      
      if (error) {
        console.error('Error starring message:', error);
        return;
      }
      
      // Update local state
      setMessages(prevMessages => 
        prevMessages.map(msg => 
          msg.id === messageId 
            ? { ...msg, isStarred: !msg.isStarred } 
            : msg
        )
      );
    } catch (error) {
      console.error('Error in toggleStarMessage:', error);
    }
  };
  
  const handleSendCoins = async () => {
    const amount = parseInt(coinAmount, 10);
    if (isNaN(amount) || amount <= 0) {
      Alert.alert('Invalid Amount', 'Please enter a valid coin amount.');
      return;
    }
    
    if (amount > coinBalance) {
      Alert.alert('Insufficient Balance', 'You do not have enough coins.');
      return;
    }
    
    try {
      // Show loading indicator
      setLoading(true);
      
      if (user?.id) {
        // Create message in database - ONLY use columns that definitely exist
        const messageData = {
          conversation_id: conversationId,
          sender_id: user.id,
          content: `Sent ${amount} Heat Coins 🔥`,
          created_at: new Date().toISOString()
          // Do NOT include type, metadata, or any other columns that might not exist
        };
        
        console.log('Sending message with data:', messageData);
        
        const { data: newMessage, error: insertError } = await supabase
          .from('messages')
          .insert(messageData)
          .select()
          .single();
        
        if (insertError) {
          console.error('Error sending gift message:', insertError);
          Alert.alert('Error', 'Failed to send coins message. Please try again.');
          setLoading(false);
          return;
        }
        
        console.log('Message created successfully:', newMessage);
        
        // Now handle the coin transfer
        try {
          // Call the direct_coin_transfer function
          const { data: result, error: transferError } = await supabase.rpc(
            'direct_coin_transfer',
            {
              sender_id: user.id,
              recipient_id: contactInfo.id,
              amount: amount
            }
          );
          
          if (transferError) {
            console.error('Error with coin transfer:', transferError);
            throw new Error('Failed to transfer coins');
          }
          
          if (result === false) {
            // Transfer failed, likely due to insufficient balance
            Alert.alert('Error', 'You don\'t have enough coins for this transfer.');
            return;
          }
          
          console.log('Coin transfer completed successfully');
        } catch (error) {
          console.error('Exception in coin transfer:', error);
          Alert.alert('Error', 'Something went wrong with the coin transfer.');
        }
        
        // Update local state
        setCoinBalance(coinBalance - amount);
        
        // Create a coin message for local state only
        const newMsg = {
          id: newMessage.id,
          text: `Sent ${amount} Heat Coins 🔥`,
          senderId: 'currentUser',
          timestamp: Date.now(),
          isStarred: false,
          type: 'gift',
          metadata: {
            type: 'coins',
            amount,
            action: 'sent'
          }
        };
        
        // Add message to conversation
        setMessages(prevMessages => [...prevMessages, newMsg]);
        
        // Close modal and reset amount
        setCoinModalVisible(false);
        setCoinAmount('100');
        
        // Show success message
        Alert.alert('Success', `You sent ${amount} Heat Coins to ${contactInfo.name}!`);
      }
    } catch (error) {
      console.error('Error sending coins:', error);
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };
  
  // Set up real-time subscription for coin balance updates
  useEffect(() => {
    if (!user?.id) return;
    
    const balanceChannel = supabase
      .channel(`public:profiles:id=eq.${user.id}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'profiles',
          filter: `id=eq.${user.id}`
        },
        (payload) => {
          console.log('Coin balance updated:', payload);
          if (payload.new && payload.new.coin_balance !== undefined) {
            setCoinBalance(payload.new.coin_balance);
          }
        }
      )
      .subscribe();
    
    return () => {
      supabase.removeChannel(balanceChannel);
    };
  }, [user?.id]);
  
  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <Ionicons name="chevron-back" size={28} color={getTintColor()} />
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.profileInfo} 
          onPress={() => router.push(`/profile/${contactInfo.id}`)}
        >
          <Image 
            source={{ 
              uri: contactInfo.avatar || 'https://via.placeholder.com/150'
            }} 
            style={styles.avatar}
            onError={(e) => {
              console.log('Avatar load error:', e.nativeEvent.error);
              setContactInfo(prev => ({
                ...prev,
                avatar: 'https://via.placeholder.com/150'
              }));
            }}
          />
          <View>
            <Text style={styles.name}>{contactInfo.name}</Text>
            <Text style={[
              styles.status, 
              contactInfo.isOnline ? styles.onlineStatus : styles.offlineStatus
            ]}>
              {isTyping ? 'Typing...' : contactInfo.isOnline ? 'Online' : 'Offline'}
            </Text>
          </View>
        </TouchableOpacity>

        {/* Call buttons removed */}
      </View>
      
      {/* Messages list */}
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
      >
        <FlatList
          ref={flatListRef}
          data={messages}
          keyExtractor={(item: ExtendedMessage) => item.id}
          style={styles.messagesList}
          contentContainerStyle={{ paddingBottom: 20 }}
          renderItem={({ item }) => (
            <MessageItem
              message={item}
              onReply={() => setReplyingTo(item)}
              onDelete={() => deleteMessage(item.id)}
              onStar={() => toggleStarMessage(item.id)}
              contactAvatar={contactInfo.avatar} // Pass the avatar URL as a prop
            />
          )}
          onContentSizeChange={() => {
            if (flatListRef.current && messages.length > 0) {
              flatListRef.current.scrollToEnd({ animated: true });
            }
          }}
        />
        
        {/* Reply indicator */}
        {replyingTo && (
          <View style={styles.replyingContainer}>
            <View style={styles.replyingToContent}>
              <Text style={styles.replyingToText}>
                Replying to: {replyingTo.text.length > 30 ? replyingTo.text.substring(0, 30) + '...' : replyingTo.text}
              </Text>
            </View>
            <TouchableOpacity onPress={() => setReplyingTo(null)}>
              <Ionicons name="close-circle" size={24} color={getTintColor()} />
            </TouchableOpacity>
          </View>
        )}
        
        {/* Input area */}
        <View style={styles.inputContainer}>
          <View style={styles.inputRow}>
            <TouchableOpacity 
              style={styles.attachButton}
              onPress={() => setAttachmentModalVisible(true)}
            >
              <Ionicons name="add-circle" size={24} color={getTintColor()} />
            </TouchableOpacity>
            
            <TextInput
              ref={inputRef}
              style={styles.input}
              placeholder="Type a message..."
              placeholderTextColor={colorScheme === 'dark' ? '#AEAEB2' : '#8E8E93'}
              value={newMessage}
              onChangeText={(text) => {
                setNewMessage(text);
                handleTyping();
              }}
              multiline
              maxLength={500}
            />
            
            {/* Gift button */}
            <TouchableOpacity 
              style={styles.giftButton}
              onPress={() => setCoinModalVisible(true)}
            >
              <Ionicons name="gift" size={24} color="#FF9500" />
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.sendButton}
              onPress={handleSendMessage}
              disabled={!newMessage.trim()}
            >
              <Ionicons 
                name="send" 
                size={24} 
                color={newMessage.trim() ? getTintColor() : colorScheme === 'dark' ? '#FFFFFF' : '#000000'} 
              />
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
      
      {/* Attachment Modal */}
      <Modal
        visible={attachmentModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setAttachmentModalVisible(false)}
      >
        <TouchableOpacity 
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setAttachmentModalVisible(false)}
        >
          <View style={styles.attachmentModal}>
            <View style={styles.attachmentHeader}>
              <Text style={styles.attachmentTitle}>Share Content</Text>
              <TouchableOpacity onPress={() => setAttachmentModalVisible(false)}>
                <Ionicons name="close" size={24} color={colorScheme === 'dark' ? '#FFFFFF' : '#000000'} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.attachmentOptions}>
              <TouchableOpacity 
                style={styles.attachmentOption}
                onPress={() => {
                  setAttachmentModalVisible(false);
                  takePhoto();
                }}
              >
                <View style={[styles.attachmentOptionIcon, { backgroundColor: '#4CD964' }]}>
                  <Ionicons name="camera" size={24} color="#FFFFFF" />
                </View>
                <Text style={styles.attachmentOptionText}>Camera</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.attachmentOption}
                onPress={() => {
                  setAttachmentModalVisible(false);
                  pickImage();
                }}
              >
                <View style={[styles.attachmentOptionIcon, { backgroundColor: '#5856D6' }]}>
                  <Ionicons name="images" size={24} color="#FFFFFF" />
                </View>
                <Text style={styles.attachmentOptionText}>Gallery</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.attachmentOption}
                onPress={async () => {
                  setAttachmentModalVisible(false);
                  try {
                    const location = await getCurrentLocation();
                    if (location) {
                      await handleSendLocationMessage(
                        location.latitude,
                        location.longitude,
                        location.name
                      );
                    }
                  } catch (error) {
                    console.error('Error sharing location:', error);
                    Alert.alert('Error', 'Failed to share location. Please try again.');
                  }
                }}
              >
                <View style={[styles.attachmentOptionIcon, { backgroundColor: '#FF2D55' }]}>
                  <Ionicons name="location" size={24} color="#FFFFFF" />
                </View>
                <Text style={styles.attachmentOptionText}>Location</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.attachmentOption}
                onPress={() => {
                  setAttachmentModalVisible(false);
                  setCoinModalVisible(true);
                }}
              >
                <View style={[styles.attachmentOptionIcon, { backgroundColor: '#FF9500' }]}>
                  <Ionicons name="gift" size={24} color="#FFFFFF" />
                </View>
                <Text style={styles.attachmentOptionText}>Send Gift</Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableOpacity>
      </Modal>
      
      {/* Coin Modal */}
      <Modal
        visible={coinModalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setCoinModalVisible(false)}
      >
        <TouchableOpacity 
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setCoinModalVisible(false)}
        >
          <View style={styles.coinModal}>
            <View style={styles.coinHeader}>
              <Text style={styles.coinTitle}>Send Heat Coins</Text>
              <TouchableOpacity onPress={() => setCoinModalVisible(false)}>
                <Ionicons name="close" size={24} color={colorScheme === 'dark' ? '#FFFFFF' : '#000000'} />
              </TouchableOpacity>
            </View>
            
            <Text style={styles.coinBalance}>Your Balance: {coinBalance} Heat Coins</Text>
            
            <TextInput
              style={styles.coinInput}
              value={coinAmount}
              onChangeText={setCoinAmount}
              keyboardType="number-pad"
              placeholder="Enter amount"
              placeholderTextColor={colorScheme === 'dark' ? '#AEAEB2' : '#8E8E93'}
            />
            
            <TouchableOpacity 
              style={[
                styles.sendCoinButton,
                (!coinAmount || parseInt(coinAmount, 10) <= 0 || parseInt(coinAmount, 10) > coinBalance) && 
                styles.disabledButton
              ]}
              onPress={handleSendCoins}
              disabled={!coinAmount || parseInt(coinAmount, 10) <= 0 || parseInt(coinAmount, 10) > coinBalance}
            >
              <Text style={styles.sendCoinButtonText}>Send Coins</Text>
            </TouchableOpacity>
            
            {coinBalance < 100 && (
              <TouchableOpacity 
                style={styles.buyMoreButton}
                onPress={() => {
                  setCoinModalVisible(false);
                  router.push('/purchase-coins');
                }}
              >
                <Text style={styles.buyMoreButtonText}>Buy More Coins</Text>
              </TouchableOpacity>
            )}
          </View>
        </TouchableOpacity>
      </Modal>
      {/* Voice Call Modal 
      <Modal
        visible={voiceCallVisible}
        animationType="slide"
        transparent={false}
        onRequestClose={handleEndVoiceCall}
      >
        <VoiceCallUI
          callState={voiceCall.callState}
          formatDuration={voiceCall.formatDuration}
          onAccept={voiceCall.acceptCall}
          onDecline={handleEndVoiceCall}
          onEnd={handleEndVoiceCall}
          onMute={voiceCall.toggleMute}
          onSpeaker={voiceCall.toggleSpeaker}
          contactName={contactInfo.name}
          contactAvatar={contactInfo.avatar}
        />
      </Modal>
      */}
    </SafeAreaView>
  );
}

// Create optimized styles with no duplicates
const createStyles = (theme: 'light' | 'dark') => {
  const colors = {
    background: theme === 'dark' ? '#1C1C1E' : '#FFFFFF',
    text: theme === 'dark' ? '#FFFFFF' : '#000000',
    secondaryText: theme === 'dark' ? '#AEAEB2' : '#8E8E93',
    inputBackground: theme === 'dark' ? '#2C2C2E' : '#F2F2F7',
    border: theme === 'dark' ? '#38383A' : '#D1D1D6',
    currentUserBubble: theme === 'dark' ? '#0A84FF' : '#007AFF',
    otherUserBubble: theme === 'dark' ? '#2C2C2E' : '#F2F2F7',
    modalBackground: theme === 'dark' ? '#2C2C2E' : '#FFFFFF',
    tint: theme === 'dark' ? '#0A84FF' : '#007AFF',
    flame: '#FF6B00',
    online: '#4CD964',
    offline: '#8E8E93',
  };
  
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    backButton: {
      marginRight: 10,
    },
    profileInfo: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
    },
    avatar: {
      width: 40,
      height: 40,
      borderRadius: 20,
      marginRight: 12,
      borderWidth: 2,
      borderColor: colors.tint,
    },
    name: {
      fontSize: 16,
      fontWeight: '600',
      color: colors.text,
    },
    status: {
      fontSize: 12,
    },
    onlineStatus: {
      color: colors.online,
      fontWeight: '500',
    },
    offlineStatus: {
      color: colors.offline,
    },
    headerButton: {
      width: 40,
      height: 40,
      justifyContent: 'center',
      alignItems: 'center',
      marginLeft: 8,
    },
    callButtons: {
      flexDirection: 'row',
    },
    callModalContainer: {
      flex: 1,
      backgroundColor: colors.background,
      justifyContent: 'center',
      alignItems: 'center',
    },
    callModalContent: {
      width: '100%',
      alignItems: 'center',
      padding: 20,
    },
    callModalAvatar: {
      width: 100,
      height: 100,
      borderRadius: 50,
      marginBottom: 20,
    },
    callModalName: {
      fontSize: 24,
      fontWeight: 'bold',
      color: colors.text,
      marginBottom: 10,
    },
    callModalStatus: {
      fontSize: 16,
      color: colors.secondaryText,
      marginBottom: 40,
    },
    callControls: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      width: '100%',
      marginTop: 20,
    },
    callControlButton: {
      width: 60,
      height: 60,
      borderRadius: 30,
      justifyContent: 'center',
      alignItems: 'center',
    },
    muteButton: {
      backgroundColor: '#555555',
    },
    endCallButton: {
      backgroundColor: '#FF3B30',
      transform: [{ rotate: '135deg' }],
    },
    speakerButton: {
      backgroundColor: '#555555',
    },
    videoCallContainer: {
      flex: 1,
      backgroundColor: '#000000',
    },
    remoteVideoContainer: {
      flex: 1,
    },
    remoteVideo: {
      flex: 1,
    },
    localVideoContainer: {
      position: 'absolute',
      top: 40,
      right: 20,
      width: 100,
      height: 150,
      borderRadius: 10,
      overflow: 'hidden',
      borderWidth: 2,
      borderColor: '#FFFFFF',
    },
    localVideo: {
      width: '100%',
      height: '100%',
    },
    videoCallControls: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'center',
      position: 'absolute',
      bottom: 40,
      left: 0,
      right: 0,
      backgroundColor: 'rgba(0,0,0,0.5)',
      padding: 20,
    },
    videoCallButton: {
      width: 50,
      height: 50,
      borderRadius: 25,
      justifyContent: 'center',
      alignItems: 'center',
    },
    cameraButton: {
      backgroundColor: '#555555',
    },
    switchCameraButton: {
      backgroundColor: '#555555',
    },
    messagesList: {
      flex: 1,
      padding: 10,
      paddingBottom: 30, // Add extra padding at the bottom of the list
    },
    inputContainer: {
      borderTopWidth: 1,
      borderTopColor: colors.border,
      padding: 10,
      marginBottom: Platform.OS === 'ios' ? 0 : 10, // Remove bottom margin on iOS
    },
    replyingContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: colors.inputBackground,
      borderRadius: 8,
      padding: 8,
      marginBottom: 10,
      marginHorizontal: 10,
    },
    replyingToContent: {
      flex: 1,
      marginRight: 8
    },
    replyingToText: {
      color: colors.text,
      fontSize: 14
    },
    inputRow: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 10,
    },
    attachButton: {
      padding: 8,
    },
    giftButton: {
      padding: 8,
      marginRight: 4,
    },
    input: {
      flex: 1,
      backgroundColor: colors.inputBackground,
      borderRadius: 20,
      paddingHorizontal: 16,
      paddingVertical: 10,
      marginHorizontal: 8,
      color: colors.text,
      maxHeight: 120
    },
    sendButton: {
      padding: 8,
    },
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'flex-end'
    },
    attachmentModal: {
      backgroundColor: colors.modalBackground,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      padding: 20
    },
    attachmentHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 20
    },
    attachmentTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text
    },
    attachmentOptions: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between'
    },
    attachmentOption: {
      width: '23%',
      alignItems: 'center',
      marginBottom: 20
    },
    attachmentOptionIcon: {
      width: 50,
      height: 50,
      borderRadius: 25,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 8
    },
    attachmentOptionText: {
      fontSize: 12,
      color: colors.text,
      textAlign: 'center'
    },
    coinModal: {
      backgroundColor: colors.modalBackground,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      padding: 20
    },
    coinHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 20
    },
    coinTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.text
    },
    coinBalance: {
      fontSize: 16,
      color: colors.text,
      marginBottom: 20,
      textAlign: 'center',
      fontWeight: '600'
    },
    coinInput: {
      backgroundColor: colors.inputBackground,
      borderRadius: 8,
      padding: 12,
      fontSize: 16,
      color: colors.text,
      marginBottom: 20,
      textAlign: 'center'
    },
    sendCoinButton: {
      backgroundColor: colors.flame,
      borderRadius: 8,
      padding: 15,
      alignItems: 'center',
      marginBottom: 15
    },
    sendCoinButtonText: {
      color: '#FFFFFF',
      fontSize: 16,
      fontWeight: '600'
    },
    disabledButton: {
      opacity: 0.5
    },
    buyMoreButton: {
      padding: 15,
      alignItems: 'center'
    },
    buyMoreButtonText: {
      color: colors.tint,
      fontSize: 16,
      fontWeight: '500'
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
      height: 300
    },
    emptyText: {
      fontSize: 18,
      fontWeight: '600',
      color: colors.secondaryText,
      marginBottom: 8
    },
    emptySubtext: {
      fontSize: 14,
      color: colors.secondaryText,
      textAlign: 'center'
    },
    giftContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 5,
    },
    giftIcon: {
      marginRight: 8,
    },
    giftText: {
      fontSize: 14,
      color: theme === 'dark' ? '#FFFFFF' : '#000000',
      fontWeight: '500',
    },
    messageImage: {
      width: '100%',
      height: 200,
      borderRadius: 12,
    },
    locationContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: 8,
    },
    locationIcon: {
      marginRight: 8,
    },
    locationText: {
      fontSize: 14,
      color: theme === 'dark' ? '#FFFFFF' : '#000000',
    },
    messageText: {
      fontSize: 16,
      color: '#FFFFFF',
      fontWeight: '400',
    },
    otherMessageText: {
      fontSize: 16,
      color: theme === 'dark' ? '#FFFFFF' : '#000000',
      fontWeight: '400',
    },
    timestamp: {
      fontSize: 10,
      color: 'rgba(255, 255, 255, 0.8)',
      alignSelf: 'flex-end',
      marginTop: 4,
    },
    otherTimestamp: {
      fontSize: 10,
      color: theme === 'dark' ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.6)',
      alignSelf: 'flex-end',
      marginTop: 4,
    },
    messageContainer: {
      flexDirection: 'row',
      marginVertical: 4,
      paddingHorizontal: 5, // Reduced from 16 to 8
    },
    currentUserContainer: {
      justifyContent: 'flex-end',
      alignSelf: 'flex-end',
    },
    otherUserContainer: {
      justifyContent: 'flex-start',
      alignSelf: 'flex-start',
    },
    messageAvatar: {
      width: 28, // Slightly smaller
      height:  28, // Slightly smaller
      borderRadius: 14,
      marginRight: -15, // Reduced from 8 to 4
    },
    messageBubble: {
      maxWidth: '80%',
      padding: 10, // Slightly reduced padding
      borderRadius: 18,
    },
    currentUserBubble: {
      backgroundColor: colors.currentUserBubble,
      borderBottomRightRadius: 4,
    },
    otherUserBubble: {
      backgroundColor: colors.otherUserBubble,
      borderBottomLeftRadius: 4,
    },
  });
};

const MessageItem = ({ 
  message, 
  onReply, 
  onDelete, 
  onStar,
  contactAvatar
}: { 
  message: ExtendedMessage; 
  onReply: () => void; 
  onDelete: () => void; 
  onStar: () => void;
  contactAvatar: string;
}) => {
  const isCurrentUser = message.senderId === 'currentUser';
  const colorScheme = useColorScheme();
  const styles = useMemo(() => createStyles(colorScheme === 'dark' ? 'dark' : 'light'), [colorScheme]);
  
  // Render different content based on message type
  const renderMessageContent = () => {
    if (message.type === 'image' && message.media) {
      return (
        <Image 
          source={{ uri: message.media.uri }} 
          style={styles.messageImage}
          resizeMode="cover"
        />
      );
    } else if (message.type === 'gift' && message.metadata?.type === 'coins') {
      // Different display for sent vs received coins
      const isSent = message.metadata.action === 'sent';
      const isReceived = message.metadata.action === 'received';
      
      return (
        <View style={styles.giftContainer}>
          <Ionicons name="gift" size={24} color="#FF9500" style={styles.giftIcon} />
          <Text style={styles.giftText}>
            {isSent 
              ? `Sent ${message.metadata.amount} Heat Coins 🔥` 
              : isReceived 
                ? `Received ${message.metadata.amount} Heat Coins 🔥` 
                : `${message.metadata.amount} Heat Coins 🔥`}
          </Text>
        </View>
      );
    } else if (message.metadata?.type === 'location') {
      return (
        <View style={styles.locationContainer}>
          <Ionicons name="location" size={24} color="#FF2D55" style={styles.locationIcon} />
          <Text style={styles.locationText}>
            {message.metadata.locationName || 'Shared Location'}
          </Text>
        </View>
      );
    } else {
      return <Text style={isCurrentUser ? styles.messageText : styles.otherMessageText}>{message.text}</Text>;
    }
  };
  
  // Format timestamp
  const formattedTime = new Date(message.timestamp).toLocaleTimeString([], { 
    hour: '2-digit', 
    minute: '2-digit' 
  });
  
  // Return JSX here
  return (
    <TouchableOpacity
      onLongPress={onReply}
      style={[
        styles.messageContainer,
        isCurrentUser ? styles.currentUserContainer : styles.otherUserContainer
      ]}
    >
      {!isCurrentUser && (
        <Image 
          source={{ uri: contactAvatar || 'https://via.placeholder.com/150' }} 
          style={styles.messageAvatar}
          onError={(e) => {
            console.log('Message avatar load error:', e.nativeEvent.error);
          }}
        />
      )}
      <View style={[
        styles.messageBubble,
        isCurrentUser ? styles.currentUserBubble : styles.otherUserBubble
      ]}>
        {renderMessageContent()}
        <Text style={isCurrentUser ? styles.timestamp : styles.otherTimestamp}>
          {formattedTime}
        </Text>
      </View>
    </TouchableOpacity>
  );
};







