{"editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.organizeImports": "explicit", "source.sortMembers": "explicit"}, "typescript.tsdk": "node_modules/typescript/lib", "typescript.disableAutomaticTypeAcquisition": false, "typescript.validate.enable": true, "typescript.reportStyleChecksAsWarnings": true, "typescript.tsserver.watchOptions": {"watchDirectory": "useFsEvents", "watchFile": "useFsEvents", "excludeDirectories": ["**/node_modules"]}, "typescript.tsserver.experimental.enableProjectDiagnostics": false, "typescript.preferences.importModuleSpecifier": "non-relative", "javascript.preferences.importModuleSpecifier": "non-relative", "typescript.workspaceSymbols.excludeLibrarySymbols": true, "typescript.surveys.enabled": false, "typescript.suggest.completeFunctionCalls": true, "typescript.updateImportsOnFileMove.enabled": "always", "typescript.preferences.quoteStyle": "single", "javascript.preferences.quoteStyle": "single", "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/Thumbs.db": true}, "search.exclude": {"**/node_modules": true, "**/bower_components": true, "**/*.code-search": true, "**/build": true, "**/dist": true}, "zencoder.enableRepoIndexing": true}