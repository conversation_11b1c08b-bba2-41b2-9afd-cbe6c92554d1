const express = require('express');
const cors = require('cors');
const crypto = require('crypto');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const app = express();
app.use(cors());
app.use(express.json());

// Add a simple health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', message: 'Server is running' });
});

// Add a UPI payment verification endpoint
app.post('/verify-upi-payment', async (req, res) => {
  try {
    const { userId, amount, coins, transactionId } = req.body;
    
    // In a real implementation, you would verify the payment with your payment gateway
    // For development, we'll just simulate a successful payment
    
    console.log(`Payment received: ${amount} for ${coins} coins from user ${userId}`);
    
    // Return success response
    res.json({ 
      success: true, 
      message: 'Payment verified successfully',
      data: {
        userId,
        coins,
        transactionId,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error verifying UPI payment:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`Development server running on port ${PORT}`);
});



