// Minimal attachment service

export interface Attachment {
  id: string;
  type: string;
  uri: string;
  name?: string;
}

export const useAttachments = () => {
  const attachments: Attachment[] = [];

  const pickImage = async () => {
    console.log('Picking image');
    return null;
  };

  const takePhoto = async () => {
    console.log('Taking photo');
    return {
      uri: 'https://example.com/photo.jpg'
    };
  };

  const pickDocument = async () => {
    console.log('Picking document');
    return null;
  };

  const getCurrentLocation = async () => {
    console.log('Getting current location');
    return {
      name: 'Current Location',
      latitude: 37.7749,
      longitude: -122.4194
    };
  };

  const addAttachment = (attachment: Attachment) => {
    console.log('Adding attachment', attachment);
  };

  const removeAttachment = (attachmentId: string) => {
    console.log('Removing attachment', attachmentId);
  };

  return {
    pickImage,
    takePhoto,
    pickDocument,
    getCurrentLocation,
    addAttachment,
    removeAttachment,
    attachments
  };
};

import axios from 'axios';

// Replace with your actual Razorpay key ID
export const RAZORPAY_KEY_ID = 'rzp_test_your_key_id';
const API_URL = 'https://your-backend-api.com'; // Replace with your actual backend URL

// Coin packages
export const coinPackages = [
  { id: 'basic', coins: 100, price: 399, currency: 'INR' },
  { id: 'standard', coins: 250, price: 799, currency: 'INR' },
  { id: 'premium', coins: 500, price: 1499, currency: 'INR' },
  { id: 'ultimate', coins: 1000, price: 2999, currency: 'INR' },
];

// Function to create a Razorpay order on your backend
export const createRazorpayOrder = async (packageId: string) => {
  try {
    const selectedPackage = coinPackages.find(pkg => pkg.id === packageId);
    if (!selectedPackage) {
      throw new Error('Invalid package selected');
    }
    
    // Call your backend to create a Razorpay order
    const response = await axios.post(`${API_URL}/create-razorpay-order`, {
      amount: selectedPackage.price * 100, // Razorpay expects amount in paise
      currency: selectedPackage.currency,
      receipt: `receipt_${Date.now()}`,
      notes: {
        packageId,
        coins: selectedPackage.coins
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error creating Razorpay order:', error);
    throw error;
  }
};

// Mock function for demo purposes - replace with actual API call in production
export const mockCreateRazorpayOrder = async (packageId: string) => {
  const selectedPackage = coinPackages.find(pkg => pkg.id === packageId);
  if (!selectedPackage) {
    throw new Error('Invalid package selected');
  }
  
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Return mock data
  return {
    id: 'order_' + Math.random().toString(36).substring(2),
    amount: selectedPackage.price * 100,
    currency: selectedPackage.currency,
    receipt: `receipt_${Date.now()}`,
  };
};

// For one-off payments
export const createOneOffOrder = async (
  amount: number, 
  currency: string = 'INR', 
  description: string,
  metadata: any = {}
) => {
  try {
    // Call your backend to create a Razorpay order
    const response = await axios.post(`${API_URL}/create-razorpay-order`, {
      amount: amount * 100, // Convert to paise
      currency: currency,
      receipt: `receipt_${Date.now()}`,
      notes: {
        description,
        ...metadata
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Error creating Razorpay order:', error);
    throw error;
  }
};

// Mock function for testing
export const mockCreateOneOffOrder = async (
  amount: number, 
  currency: string = 'INR', 
  description: string,
  metadata: any = {}
) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Return mock order data
  return {
    id: 'order_' + Math.random().toString(36).substring(2),
    amount: amount * 100,
    currency: currency,
    receipt: `receipt_${Date.now()}`,
  };
};

// Add GST calculation for India
export const calculateGST = (amount: number): { baseAmount: number, gstAmount: number, totalAmount: number } => {
  // GST rate for online services is typically 18%
  const gstRate = 0.18;
  const baseAmount = amount / (1 + gstRate);
  const gstAmount = amount - baseAmount;
  
  return {
    baseAmount: Math.round(baseAmount),
    gstAmount: Math.round(gstAmount),
    totalAmount: amount
  };
};

