import { supabase } from '../lib/supabase';

// Constants
export const FREE_DAILY_CARDS = 4;
export const SUBSCRIPTION_COST = 150;

// Interface for subscription status
export interface SubscriptionStatus {
  isSubscribed: boolean;
  cardsViewedToday: number;
  freeCardsRemaining: number;
  subscriptionEndDate: string | null;
  subscriptionType: string | null; // Add this field
}

// Check if user has an active subscription
export const checkSubscription = async (userId: string): Promise<SubscriptionStatus> => {
  try {
    // Get user's subscription data
    const { data: subscriptionData, error: subscriptionError } = await supabase
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (subscriptionError && subscriptionError.code !== 'PGRST116') {
      console.error('Error checking subscription:', subscriptionError);
      throw subscriptionError;
    }
    
    // Get today's card view count
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const { data: viewData, error: viewError } = await supabase
      .from('daily_card_views')
      .select('view_count')
      .eq('user_id', userId)
      .eq('view_date', today)
      .single();
    
    if (viewError && viewError.code !== 'PGRST116') {
      console.error('Error checking daily views:', viewError);
      throw viewError;
    }
    
    const cardsViewedToday = viewData?.view_count || 0;
    
    // Check if user is subscribed using the expires_at column
    let isSubscribed = false;
    let subscriptionEndDate = null;
    let subscriptionType = null;
    
    if (subscriptionData) {
      if (subscriptionData.expires_at) {
        isSubscribed = new Date(subscriptionData.expires_at) > new Date();
        subscriptionEndDate = subscriptionData.expires_at;
        subscriptionType = subscriptionData.subscription_type || null;
        
        // Log the subscription type for debugging
        console.log('Current subscription type:', subscriptionType);
      }
    }
    
    return {
      isSubscribed,
      cardsViewedToday,
      freeCardsRemaining: Math.max(0, FREE_DAILY_CARDS - cardsViewedToday),
      subscriptionEndDate,
      subscriptionType
    };
  } catch (error) {
    console.error('Error in checkSubscription:', error);
    throw error;
  }
};

// Record a card view
export const recordCardView = async (userId: string): Promise<boolean> => {
  try {
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    
    // Check if record exists for today
    const { data: existingRecord, error: checkError } = await supabase
      .from('daily_card_views')
      .select('*')
      .eq('user_id', userId)
      .eq('view_date', today)
      .single();
    
    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking daily view record:', checkError);
      throw checkError;
    }
    
    if (existingRecord) {
      // Update existing record
      const { error: updateError } = await supabase
        .from('daily_card_views')
        .update({ view_count: existingRecord.view_count + 1 })
        .eq('id', existingRecord.id);
      
      if (updateError) {
        console.error('Error updating view count:', updateError);
        throw updateError;
      }
    } else {
      // Create new record
      const { error: insertError } = await supabase
        .from('daily_card_views')
        .insert({
          user_id: userId,
          view_date: today,
          view_count: 1
        });
      
      if (insertError) {
        console.error('Error creating view record:', insertError);
        throw insertError;
      }
    }
    
    return true;
  } catch (error) {
    console.error('Error in recordCardView:', error);
    return false;
  }
};

// Purchase subscription
export const purchaseSubscription = async (userId: string, durationDays: number = 30): Promise<boolean> => {
  try {
    // Get user's coin balance
    const { data: userData, error: userError } = await supabase
      .from('profiles')
      .select('coin_balance')
      .eq('id', userId)
      .single();
    
    if (userError) {
      console.error('Error fetching user data:', userError);
      throw userError;
    }
    
    if (!userData || userData.coin_balance < SUBSCRIPTION_COST) {
      return false; // Not enough coins
    }
    
    // Calculate end date
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + durationDays);
    const endDateString = endDate.toISOString();
    
    // Start a transaction
    // 1. Deduct coins
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ coin_balance: userData.coin_balance - SUBSCRIPTION_COST })
      .eq('id', userId);
    
    if (updateError) {
      console.error('Error updating coin balance:', updateError);
      throw updateError;
    }
    
    // 2. Record transaction
    const { error: transactionError } = await supabase
      .from('coin_transactions')
      .insert({
        user_id: userId,
        amount: -SUBSCRIPTION_COST,
        transaction_type: 'subscription'
      });
    
    if (transactionError) {
      console.error('Error recording transaction:', transactionError);
      // Continue even if transaction logging fails
    }
    
    // 3. Create or update subscription using the correct column name: expires_at
    const { data: existingSub, error: subCheckError } = await supabase
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (subCheckError && subCheckError.code !== 'PGRST116') {
      console.error('Error checking existing subscription:', subCheckError);
      throw subCheckError;
    }
    
    if (existingSub) {
      // If there's an existing subscription, extend it
      const { error: updateSubError } = await supabase
        .from('user_subscriptions')
        .update({ expires_at: endDateString })
        .eq('id', existingSub.id);
      
      if (updateSubError) {
        console.error('Error updating subscription:', updateSubError);
        throw updateSubError;
      }
    } else {
      // Create new subscription with the correct column name
      const { error: createSubError } = await supabase
        .from('user_subscriptions')
        .insert({
          user_id: userId,
          created_at: new Date().toISOString(),
          expires_at: endDateString,
          subscription_type: 'premium'
        });
      
      if (createSubError) {
        console.error('Error creating subscription:', createSubError);
        throw createSubError;
      }
    }
    
    return true;
  } catch (error) {
    console.error('Error in purchaseSubscription:', error);
    throw error;
  }
};




