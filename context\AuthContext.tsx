import { Session, User as SupabaseUser } from '@supabase/supabase-js';
import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '../lib/supabase';

// Define user type
export interface SocialMediaLinks {
  instagram?: string;
  facebook?: string;
}

export interface User {
  id: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  phoneNumber?: string;
  bio?: string;
  gender?: string;
  preference?: string;
  photos?: string[];
  coinBalance?: number;
  socialMedia?: SocialMediaLinks;
  occupation?: string;
  education?: string;
  location?: string;
  birthdate?: string;
  hobbies?: string[];
}

// Define context type
interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<any>;
  register: (email: string, password: string, displayName: string) => Promise<any>;
  logout: () => Promise<void>;
  updateUserProfile: (profile: Partial<User>) => Promise<void>;
  getUserPhotos: (uid: string) => Promise<string[]>;
  sendVerificationCode: (phoneNumber: string) => Promise<string>;
  verifyCode: (verificationId: string, code: string, profile?: Partial<User>) => Promise<void>;
}

// Create the context
const AuthContext = createContext<AuthContextType | null>(null);

// Create a provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      console.log('Initial session check:', session ? 'Logged in' : 'No session');
      setSession(session);
      if (session?.user) {
        fetchUserProfile(session.user);
      }
      setLoading(false);
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      console.log('Auth state changed:', _event, session ? 'Logged in' : 'No session');
      setSession(session);
      if (session?.user) {
        fetchUserProfile(session.user);
      } else {
        setUser(null);
      }
      setLoading(false);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // Fetch user profile from Supabase
  const fetchUserProfile = async (supabaseUser: SupabaseUser) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*, social_media_links(*), occupation, education, location, birthdate, hobbies')
        .eq('id', supabaseUser.id)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is the error code for "no rows returned"
        console.error('Error fetching user profile:', error);
        return;
      }

      if (data) {
        setUser({
          id: supabaseUser.id,
          email: supabaseUser.email || '',
          displayName: data.display_name,
          photoURL: data.avatar_url,
          phoneNumber: data.phone_number,
          bio: data.bio,
          gender: data.gender,
          preference: data.preference,
          photos: data.photos,
          coinBalance: data.coin_balance,
          socialMedia: data.social_media_links || { instagram: '', facebook: '' },
          occupation: data.occupation,
          education: data.education,
          location: data.location,
          birthdate: data.birthdate,
          hobbies: data.hobbies
        });
      } else {
        // Profile doesn't exist, create it
        const { error: createError } = await supabase
          .from('profiles')
          .insert([
            {
              id: supabaseUser.id,
              email: supabaseUser.email,
              display_name: supabaseUser.user_metadata?.display_name || supabaseUser.email?.split('@')[0],
              created_at: new Date().toISOString(),
            },
          ]);

        if (createError) {
          console.error('Error creating profile:', createError);
          return;
        }

        // Set basic user info
        setUser({
          id: supabaseUser.id,
          email: supabaseUser.email || '',
          displayName: supabaseUser.user_metadata?.display_name || supabaseUser.email?.split('@')[0],
          photoURL: '',
          phoneNumber: '',
          bio: '',
          gender: '',
          preference: '',
          photos: [],
          coinBalance: 0,
          socialMedia: { instagram: '', facebook: '' },
          occupation: '',
          education: '',
          location: '',
          birthdate: '',
          hobbies: []
        });
      }
    } catch (error) {
      console.error('Error in fetchUserProfile:', error);
    }
  };

  // Login function
  const login = async (email: string, password: string) => {
    setLoading(true);
    try {
      // Sign in with email and password
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;
      
      // Set session and user immediately to avoid navigation delay
      setSession(data.session);
      if (data.session?.user) {
        await fetchUserProfile(data.session.user);
      }
      
      return data;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Register function
  const register = async (email: string, password: string, displayName: string) => {
    setLoading(true);
    try {
      // Sign up with email and password
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            display_name: displayName,
          },
        }
      });

      if (error) throw error;
      
      return data;
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      // Clear user and session immediately
      setUser(null);
      setSession(null);
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  };

  // Update user profile
  const updateUserProfile = async (profile: Partial<User>) => {
    if (!user) return;

    try {
      const updates = {
        id: user.id,
        display_name: profile.displayName,
        avatar_url: profile.photoURL,
        phone_number: profile.phoneNumber,
        bio: profile.bio,
        gender: profile.gender,
        preference: profile.preference,
        photos: profile.photos,
        coin_balance: profile.coinBalance,
        occupation: profile.occupation,
        education: profile.education,
        location: profile.location,
        birthdate: profile.birthdate,
        hobbies: profile.hobbies,
        updated_at: new Date(),
      };

      const { error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.id);

      if (error) throw error;

      // Update local user state
      setUser({ ...user, ...profile });
    } catch (error) {
      console.error('Update profile error:', error);
      throw error;
    }
  };

  // Get user photos
  const getUserPhotos = async (uid: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('photos')
        .eq('id', uid)
        .single();

      if (error) throw error;
      return data.photos || [];
    } catch (error) {
      console.error('Get photos error:', error);
      return [];
    }
  };

  // Phone verification methods
  // Note: Supabase doesn't have built-in phone verification, so you might need a third-party service
  // These are placeholder implementations
  const sendVerificationCode = async (phoneNumber: string) => {
    // Implement with a third-party SMS service or use Supabase Functions
    console.log('Sending verification code to', phoneNumber);
    return 'verification-id-' + Date.now();
  };

  const verifyCode = async (verificationId: string, code: string, profile?: Partial<User>) => {
    // Implement with a third-party SMS service or use Supabase Functions
    console.log('Verifying code', code, 'for verification ID', verificationId);
    
    if (profile && user) {
      await updateUserProfile(profile);
    }
  };

  return (
    <AuthContext.Provider value={{ 
      user, 
      session,
      loading, 
      login, 
      register, 
      logout, 
      updateUserProfile,
      getUserPhotos,
      verifyCode,
      sendVerificationCode
    }}>
      {children}
    </AuthContext.Provider>
  );
};

// Create a hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};












