import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';

interface VoiceCallUIProps {
  callState: 'connecting' | 'ringing' | 'ongoing' | 'ended';
  formatDuration: () => string;
  onAccept: () => void;
  onDecline: () => void;
  onEnd: () => void;
  onMute: () => void;
  onSpeaker: () => void;
}

export function VoiceCallUI({
  callState,
  formatDuration,
  onAccept,
  onDecline,
  onEnd,
  onMute,
  onSpeaker
}: VoiceCallUIProps) {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.profileContainer}>
        <Image 
          source={{ uri: 'https://randomuser.me/api/portraits/women/44.jpg' }} 
          style={styles.profileImage} 
        />
        <Text style={styles.callerName}><PERSON></Text>
        <Text style={styles.callStatus}>
          {callState === 'connecting' && 'Connecting...'}
          {callState === 'ringing' && 'Ringing...'}
          {callState === 'ongoing' && formatDuration()}
          {callState === 'ended' && 'Call ended'}
        </Text>
      </View>
      
      <View style={styles.controls}>
        {callState === 'ringing' ? (
          <>
            <TouchableOpacity style={[styles.controlButton, styles.declineButton]} onPress={onDecline}>
              <Ionicons name="close" size={30} color="#FFFFFF" />
            </TouchableOpacity>
            <TouchableOpacity style={[styles.controlButton, styles.acceptButton]} onPress={onAccept}>
              <Ionicons name="call" size={30} color="#FFFFFF" />
            </TouchableOpacity>
          </>
        ) : (
          <>
            <TouchableOpacity style={styles.controlButton} onPress={onMute}>
              <Ionicons name="mic-off" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            <TouchableOpacity style={[styles.controlButton, styles.endButton]} onPress={onEnd}>
              <Ionicons name="call" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.controlButton} onPress={onSpeaker}>
              <Ionicons name="volume-high" size={24} color="#FFFFFF" />
            </TouchableOpacity>
          </>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1E1E1E',
    justifyContent: 'space-between',
    padding: 20,
  },
  profileContainer: {
    alignItems: 'center',
    marginTop: 60,
  },
  profileImage: {
    width: 120,
    height: 120,
    borderRadius: 60,
    marginBottom: 20,
  },
  callerName: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  callStatus: {
    color: '#CCCCCC',
    fontSize: 16,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 40,
  },
  controlButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#555555',
    justifyContent: 'center',
    alignItems: 'center',
  },
  acceptButton: {
    backgroundColor: '#4CAF50',
  },
  declineButton: {
    backgroundColor: '#F44336',
  },
  endButton: {
    backgroundColor: '#F44336',
  },
});