import React from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Message } from '../../services/MessageService';

interface MessageItemProps {
  message: Message;
  onReply: () => void;
  onDelete: () => void;
  onStar: () => void;
  contactAvatar: string | null;
}

const MessageItem: React.FC<MessageItemProps> = ({
  message,
  onReply,
  onDelete,
  onStar,
  contactAvatar = null // Change to null instead of URL
}) => {
  const isCurrentUser = message.senderId === 'currentUser';
  const formattedTime = new Date(message.timestamp).toLocaleTimeString([], { 
    hour: '2-digit', 
    minute: '2-digit' 
  });

  const renderMessageContent = () => {
    if (message.type === 'image' && message.media) {
      return (
        <Image 
          source={{ uri: message.media.uri }} 
          style={styles.messageImage}
          resizeMode="cover"
          // Remove defaultSource prop
          // Add error handling
          onError={(e) => {
            console.log('Message image load error:', e.nativeEvent.error);
          }}
        />
      );
    } else if (message.type === 'gift' && message.metadata?.type === 'coins') {
      // Gift message rendering...
    } else {
      return <Text style={isCurrentUser ? styles.messageText : styles.otherMessageText}>{message.text}</Text>;
    }
  };

  return (
    <TouchableOpacity
      onLongPress={onReply}
      style={[
        styles.container,
        isCurrentUser ? styles.currentUserContainer : styles.otherUserContainer
      ]}
    >
      {!isCurrentUser && (
        <Image 
          source={{ uri: contactAvatar || 'https://via.placeholder.com/32' }} 
          style={styles.avatar}
          // Remove defaultSource prop
          onError={(e) => {
            console.log('Message avatar load error:', e.nativeEvent.error);
          }}
        />
      )}
      <View style={[
        styles.bubble,
        isCurrentUser ? styles.currentUserBubble : styles.otherUserBubble
      ]}>
        {renderMessageContent()}
        <Text style={isCurrentUser ? styles.timestamp : styles.otherTimestamp}>
          {formattedTime}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

// Update the message bubble styles for better visibility
const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginVertical: 4,
    paddingHorizontal: 16,
  },
  currentUserContainer: {
    justifyContent: 'flex-end',
  },
  otherUserContainer: {
    justifyContent: 'flex-start',
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
  },
  bubble: {
    maxWidth: '80%',
    padding: 12,
    borderRadius: 18,
  },
  currentUserBubble: {
    backgroundColor: '#4A90E2', // Brighter blue for better visibility
    borderBottomRightRadius: 4,
  },
  otherUserBubble: {
    backgroundColor: '#E5E5EA', // Lighter gray for better contrast
    borderBottomLeftRadius: 4,
  },
  messageText: {
    fontSize: 16,
    color: '#FFFFFF', // White text for sent messages
    fontWeight: '400',
  },
  otherMessageText: {
    fontSize: 16,
    color: '#000000', // Black text for received messages
    fontWeight: '400',
  },
  timestamp: {
    fontSize: 10,
    color: 'rgba(255, 255, 255, 0.8)', // More visible white timestamp
    alignSelf: 'flex-end',
    marginTop: 4,
  },
  otherTimestamp: {
    fontSize: 10,
    color: 'rgba(0, 0, 0, 0.6)', // More visible black timestamp
    alignSelf: 'flex-end',
    marginTop: 4,
  },
  messageImage: {
    width: '100%',
    height: 200,
    borderRadius: 10,
  },
});

export default MessageItem;











