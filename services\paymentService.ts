import { supabase } from '../lib/supabase';

export interface CoinPackage {
  id: string;
  name: string;
  coins: number;
  price: number;
  discountedPrice?: number;
  popular?: boolean;
  bonusCoins?: number;
  offerText?: string;
}

// Define coin packages
export const coinPackages: CoinPackage[] = [
  {
    id: 'starter',
    name: 'Starter Pack',
    coins: 100,
    price: 99,
    bonusCoins: 0
  },
  {
    id: 'standard',
    name: 'Standard Pack',
    coins: 500,
    price: 499,
    discountedPrice: 449,
    popular: true,
    bonusCoins: 50,
    offerText: 'SPECIAL OFFER: +50 Bonus Coins'
  },
  {
    id: 'premium',
    name: 'Premium Pack',
    coins: 1000,
    price: 999,
    discountedPrice: 899,
    bonusCoins: 100,
    offerText: 'SPECIAL OFFER: +100 Bonus Coins'
  },
  {
    id: 'ultimate',
    name: 'Ultimate Pack',
    coins: 2500,
    price: 2499,
    discountedPrice: 1999,
    bonusCoins: 250,
    offerText: 'SPECIAL OFFER: +250 Bonus Coins'
  },
];

// Function to handle successful payment - updated to use profiles table instead of users
export async function handleSuccessfulPayment(userId: string, coins: number): Promise<boolean> {
  try {
    // Get current user data from profiles table
    const { data: userData, error: fetchError } = await supabase
      .from('profiles')
      .select('coin_balance')
      .eq('id', userId)
      .single();
    
    if (fetchError) {
      console.error('Error fetching user data:', fetchError);
      return false;
    }
    
    // Calculate new coin balance
    const currentCoins = userData?.coin_balance || 0;
    const newCoinBalance = currentCoins + coins;
    
    // Update user's coin balance in profiles table
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ coin_balance: newCoinBalance })
      .eq('id', userId);
    
    if (updateError) {
      console.error('Error updating coin balance:', updateError);
      return false;
    }
    
    // Record the transaction
    const { error: transactionError } = await supabase
      .from('coin_transactions')
      .insert({
        user_id: userId,
        amount: coins,
        transaction_type: 'purchase',
        description: `Purchased ${coins} Heat Coins`,
      });
    
    if (transactionError) {
      console.error('Error recording transaction:', transactionError);
      // We still return true because the coins were added successfully
    }
    
    return true;
  } catch (error) {
    console.error('Error in handleSuccessfulPayment:', error);
    return false;
  }
}

// Function to verify UPI payment
export async function verifyUpiPayment(
  userId: string,
  amount: number,
  coins: number,
  transactionId: string
): Promise<boolean> {
  try {
    // In a real app, you would verify the payment with your payment gateway
    // For development/testing, we'll simulate a successful verification
    
    // Record the payment in the database
    const { error } = await supabase
      .from('payments')
      .insert({
        user_id: userId,
        amount,
        coins,
        transaction_id: transactionId,
        payment_method: 'upi',
        status: 'completed',
      });
    
    if (error) {
      console.error('Error recording payment:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error in verifyUpiPayment:', error);
    return false;
  }
}

// Function to get user's coin balance from Supabase
export const getUserCoinBalance = async (userId: string): Promise<number> => {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('coin_balance')
      .eq('id', userId)
      .single();
    
    if (error) {
      console.error('Error fetching coin balance:', error);
      return 0;
    }
    
    return data?.coin_balance || 0;
  } catch (error) {
    console.error('Error in getUserCoinBalance:', error);
    return 0;
  }
};

// Function to update user's coin balance in Supabase
export const updateUserCoinBalance = async (userId: string, newCoins: number): Promise<boolean> => {
  try {
    // First get current balance
    const currentBalance = await getUserCoinBalance(userId);
    const updatedBalance = currentBalance + newCoins;
    
    const { error } = await supabase
      .from('profiles')
      .update({ coin_balance: updatedBalance })
      .eq('id', userId);
    
    if (error) {
      console.error('Error updating coin balance:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error in updateUserCoinBalance:', error);
    return false;
  }
};

// The duplicate handleSuccessfulPayment function has been removed
// The first implementation above will be used





