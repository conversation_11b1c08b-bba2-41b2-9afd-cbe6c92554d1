import React, { createContext, useState, useContext, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define available languages
export type Language = 'en' | 'es' | 'fr' | 'de' | 'it' | 'pt' | 'ru' | 'zh' | 'ja' | 'ko' | 'ar' | 'hi';

// Define language names for display
export const languageNames: Record<Language, string> = {
  en: 'English',
  es: 'Spanish',
  fr: 'French',
  de: 'German',
  it: 'Italian',
  pt: 'Portuguese',
  ru: 'Russian',
  zh: 'Chinese',
  ja: 'Japanese',
  ko: 'Korean',
  ar: 'Arabic',
  hi: 'Hindi'
};

// Define translations
type Translations = {
  [key: string]: {
    [key in Language]?: string;
  };
};

// Basic translations for demonstration
const translations: Translations = {
  'common.hello': {
    en: 'Hello',
    es: 'Hola',
    fr: 'Bonjour',
    de: 'Hallo',
    it: 'Ciao',
    pt: 'Olá',
    ru: 'Привет',
    zh: '你好',
    ja: 'こんにちは',
    ko: '안녕하세요',
    ar: 'مرحبا',
    hi: 'नमस्ते'
  },
  'common.settings': {
    en: 'Settings',
    es: 'Configuración',
    fr: 'Paramètres',
    de: 'Einstellungen',
    it: 'Impostazioni',
    pt: 'Configurações',
    ru: 'Настройки',
    zh: '设置',
    ja: '設定',
    ko: '설정',
    ar: 'إعدادات',
    hi: 'सेटिंग्स'
  },
  'common.profile': {
    en: 'Profile',
    es: 'Perfil',
    fr: 'Profil',
    de: 'Profil',
    it: 'Profilo',
    pt: 'Perfil',
    ru: 'Профиль',
    zh: '个人资料',
    ja: 'プロフィール',
    ko: '프로필',
    ar: 'الملف الشخصي',
    hi: 'प्रोफ़ाइल'
  }
  // Add more translations as needed
};

// Create the context
type LanguageContextType = {
  language: Language;
  setLanguage: (language: Language) => Promise<void>;
  t: (key: string) => string;
  availableLanguages: typeof languageNames;
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Storage key
const LANGUAGE_STORAGE_KEY = 'app_language';

// Provider component
export const LanguageProvider: React.FC<{children: React.ReactNode}> = ({ children }) => {
  const [language, setLanguageState] = useState<Language>('en');
  const [isLoading, setIsLoading] = useState(true);

  // Load saved language on mount
  useEffect(() => {
    const loadLanguage = async () => {
      try {
        const savedLanguage = await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY);
        if (savedLanguage && Object.keys(languageNames).includes(savedLanguage)) {
          setLanguageState(savedLanguage as Language);
        }
      } catch (error) {
        console.error('Failed to load language preference:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadLanguage();
  }, []);

  // Set language and save to storage
  const setLanguage = async (newLanguage: Language) => {
    try {
      await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, newLanguage);
      setLanguageState(newLanguage);
      console.log(`Language changed to ${languageNames[newLanguage]} (${newLanguage})`);
      return Promise.resolve();
    } catch (error) {
      console.error('Failed to save language preference:', error);
      return Promise.reject(error);
    }
  };

  // Translation function
  const t = (key: string): string => {
    if (!translations[key]) {
      console.warn(`Translation key not found: ${key}`);
      return key;
    }

    return translations[key][language] || translations[key]['en'] || key;
  };

  // Skip rendering until language is loaded
  if (isLoading) {
    return null;
  }

  return (
    <LanguageContext.Provider value={{ 
      language, 
      setLanguage, 
      t,
      availableLanguages: languageNames
    }}>
      {children}
    </LanguageContext.Provider>
  );
};

// Custom hook to use the language context
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};