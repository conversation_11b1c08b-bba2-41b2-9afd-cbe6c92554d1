// Polyfills for Node.js core modules
global.Buffer = require('buffer').Buffer;
global.process = require('process');

if (typeof process.version === 'undefined') {
  process.version = '';
}

if (typeof process.env === 'undefined') {
  process.env = {};
}

// Required for some modules
if (!global.setImmediate) {
  global.setImmediate = setTimeout;
}

if (!global.clearImmediate) {
  global.clearImmediate = clearTimeout;
}