export default {
  expo: {
    // ... other config
    extra: {
      googlePlacesApiKey: process.env.GOOGLE_PLACES_API_KEY,
      supabaseUrl: process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://bysprhpdcowoogvcgjkj.supabase.co',
      supabaseAnonKey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ5c3ByaHBkY293b29ndmNnamtqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyNDgzNjYsImV4cCI6MjA2MjgyNDM2Nn0.j_Jr3nNagnts98ouLIC13YJy-xn0PlSNOoTy2Vc6ldM',
      eas: {
        projectId: "d979c354-e4b6-420b-afa4-8ecbda792ded"
      }
    },
    android: {
      package: "com.gokulraj916.heatwaves",
      adaptiveIcon: {
        foregroundImage: "./assets/icon.png", // Changed to use root icon instead
        backgroundColor: "#ffffff"
      },
      permissions: [
        "INTERNET",
        "ACCESS_FINE_LOCATION"
      ]
    },
    ios: {
      bundleIdentifier: "com.gokulraj916.heatwaves",
      supportsTablet: true,
      infoPlist: {
        NSLocationWhenInUseUsageDescription: "We need your location to find hangout spots near you"
      }
    },
    icon: "./assets/icon.png", // Add this line
  },
};






