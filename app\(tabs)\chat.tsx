import { BlurView } from 'expo-blur';
import * as Contacts from 'expo-contacts';
import { useRouter } from 'expo-router';
import { useCallback, useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    Dimensions,
    FlatList,
    Image,
    Keyboard,
    Linking, Platform,
    RefreshControl,
    Share,
    StyleSheet,
    Text,
    TextInput,
    TouchableOpacity,
    TouchableWithoutFeedback,
    useColorScheme,
    View
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { IconSymbol } from '../../components/ui/IconSymbol';
import { Colors } from '../../constants/Colors';
import { useAuth } from '../../context/AuthContext';
import { useResponsiveStyles } from '../../hooks/useResponsiveStyles';
import { supabase } from '../../lib/supabase';
import { ChatParticipant } from '../../services/chatService';

// Define types for our UI data
interface ConversationUI {
  id: string;
  name: string;
  avatar?: string;
  lastMessage?: string;
  time?: string;
  unread: number;
  isOnline: boolean;
}

interface UserStatus {
  userId: string;
  userName: string;
  userAvatar: string;
  hasUnseenStatus: boolean;
}

interface Contact {
  id: string; // Keep this as required
  name: string;
  avatar?: string;
  phoneNumber?: string;
  isAppUser: boolean;
  email?: string;
  userId?: string; // Add userId field to store the Supabase user ID
}

// Define the User type to match Supabase's structure
interface SupabaseUser {
  id: string;
  email?: string;
  app_metadata: {
    provider?: string;
    [key: string]: any;
  };
  user_metadata: {
    name?: string;
    avatar_url?: string;
    [key: string]: any;
  };
  aud: string;
}

// Define the status data structure from Supabase
interface UserStatusData {
  user_id: string;
  created_at: string;
  user_profile?: {
    id: string;
    display_name: string;
    avatar_url: string;
  };
}

// Import your conversation and contact data/services here

export default function ChatScreen() {
  const router = useRouter();
  const colorScheme = useColorScheme() || 'light';
  const insets = useSafeAreaInsets();
  const { dimensions, spacing, fontSizes, isLargeDevice, isLandscape } = useResponsiveStyles();
  const auth = useAuth();
  
  // Create styles with the component variables
  const styles = createStyles(insets, fontSizes, colorScheme);
  
  // State variables
  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showNewMessageModal, setShowNewMessageModal] = useState(false);
  const [isContactsLoading, setIsContactsLoading] = useState(false);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);
  
  // Chat state
  const [conversationsUI, setConversationsUI] = useState<ConversationUI[]>([]);
  const [isLoadingConversations, setIsLoadingConversations] = useState(true);
  const [participants, setParticipants] = useState<Record<string, ChatParticipant>>({});
  
  // Add state for the new message input
  const [newMessage, setNewMessage] = useState('');
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null);
  
  // Function to get dynamic colors based on color scheme
  const getDynamicColor = (colorName: string) => {
    if (colorScheme === 'dark') {
      return Colors.dark[colorName as keyof typeof Colors.dark] || Colors.dark.text;
    } else {
      return Colors.light[colorName as keyof typeof Colors.light] || Colors.light.text;
    }
  };

  // Load conversations from chat service
  const loadConversations = useCallback(async () => {
    try {
      setIsLoadingConversations(true);
      
      if (!auth?.user?.id) {
        setConversationsUI([]);
        setIsLoadingConversations(false);
        return;
      }
      
      // Get conversations from Supabase
      const { data: conversationsData, error: conversationsError } = await supabase
        .from('conversations')
        .select('*')
        .or(`participant1_id.eq.${auth.user?.id || ''},participant2_id.eq.${auth.user?.id || ''}`)
        .order('updated_at', { ascending: false });
      
      if (conversationsError) {
        console.error('Error fetching conversations:', conversationsError);
        setConversationsUI([]);
        setIsLoadingConversations(false);
        return;
      }
      
      if (!conversationsData || conversationsData.length === 0) {
        setConversationsUI([]);
        setIsLoadingConversations(false);
        return;
      }
      
      // Fetch participants data
      const participantIds = new Set<string>();
      conversationsData.forEach(conv => {
        const otherParticipantId = conv.participant1_id === auth.user?.id 
          ? conv.participant2_id 
          : conv.participant1_id;
        participantIds.add(otherParticipantId);
      });
      
      // Fetch profiles for all participants
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('id, display_name, avatar_url')
        .in('id', Array.from(participantIds));
      
      if (profilesError) {
        console.error('Error fetching participant profiles:', profilesError);
      }
      
      // Create participants map
      const participantsMap: Record<string, ChatParticipant> = {};
      if (profilesData) {
        profilesData.forEach(profile => {
          participantsMap[profile.id] = {
            id: profile.id,
            displayName: profile.display_name || 'Unknown User',
            photoURL: profile.avatar_url,
            lastSeen: undefined, // Set to undefined since the column doesn't exist
            isOnline: false // Default to offline
          };
        });
      }
      
      setParticipants(participantsMap);
      
      // Fetch last messages for each conversation
      const conversationsWithMessages = await Promise.all(
        conversationsData.map(async (conv) => {
          const { data: messagesData, error: messagesError } = await supabase
            .from('messages')
            .select('*')
            .eq('conversation_id', conv.id)
            .order('created_at', { ascending: false })
            .limit(1);
          
          if (messagesError) {
            console.error(`Error fetching messages for conversation ${conv.id}:`, messagesError);
            return { ...conv, lastMessage: null };
          }
          
          return { 
            ...conv, 
            lastMessage: messagesData && messagesData.length > 0 ? messagesData[0] : null 
          };
        })
      );
      
      // Transform conversations to UI format
      const conversationsUIData = conversationsWithMessages.map(conv => {
        const otherParticipantId = conv.participant1_id === auth.user?.id 
          ? conv.participant2_id 
          : conv.participant1_id;
        
        const participant = participantsMap[otherParticipantId] || {
          id: otherParticipantId,
          displayName: 'Unknown User',
          isOnline: false
        };
        
        return {
          id: conv.id,
          name: participant.displayName,
          avatar: participant.photoURL,
          lastMessage: conv.lastMessage?.content || 'No messages yet',
          time: conv.lastMessage ? formatMessageTime(new Date(conv.lastMessage.created_at).getTime()) : '',
          unread: conv.unread_count || 0,
          isOnline: participant.isOnline
        };
      });
      
      setConversationsUI(conversationsUIData);
    } catch (error) {
      console.error('Error loading conversations:', error);
      setConversationsUI([]);
    } finally {
      setIsLoadingConversations(false);
    }
  }, [auth?.user?.id]);
  
  // Format message timestamp
  const formatMessageTime = (timestamp: number): string => {
    const now = new Date();
    const messageDate = new Date(timestamp);
    
    // Check if it's today
    if (messageDate.toDateString() === now.toDateString()) {
      return messageDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
    
    // Check if it's yesterday
    const yesterday = new Date(now);
    yesterday.setDate(now.getDate() - 1);
    if (messageDate.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    }
    
    // Check if it's within the last week
    const oneWeekAgo = new Date(now);
    oneWeekAgo.setDate(now.getDate() - 7);
    if (messageDate > oneWeekAgo) {
      const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      return days[messageDate.getDay()];
    }
    
    // Otherwise, return the date
    return messageDate.toLocaleDateString();
  };

  // Functions for handling contacts, navigation, etc.
  const loadContacts = useCallback(async () => {
    setIsContactsLoading(true);
    
    try {
      // Request permission to access contacts
      const { status } = await Contacts.requestPermissionsAsync();
      
      if (status === 'granted') {
        // Fetch contacts from the device
        const { data } = await Contacts.getContactsAsync({
          fields: [
            Contacts.Fields.ID,
            Contacts.Fields.Name,
            Contacts.Fields.PhoneNumbers,
            Contacts.Fields.Emails,
            Contacts.Fields.Image,
          ],
        });
        
        if (data.length > 0) {
          // Transform Expo contacts to our app's contact format
          const appContacts: Contact[] = [];
          
          for (const contact of data) {
            // Skip contacts without phone numbers
            if (!contact.phoneNumbers || contact.phoneNumbers.length === 0) {
              continue;
            }
            
            // Get the first phone number
            const phoneNumber = contact.phoneNumbers?.[0]?.number || '';
            
            // Check if this contact is an app user by querying Supabase
            let isAppUser = false;
            let userId = '';

            try {
              if (phoneNumber) {
                // Format phone number to E.164 format (remove spaces, dashes, etc.)
                const formattedPhoneNumber = phoneNumber.replace(/\D/g, '');
                
                // Query Supabase to check if this phone number exists
                const { data: userData, error: userError } = await supabase
                  .from('profiles')
                  .select('id')
                  .eq('phone_number', formattedPhoneNumber)
                  .limit(1);
                
                if (!userError && userData && userData.length > 0) {
                  isAppUser = true;
                  userId = userData[0].id;
                }
              }
            } catch (error) {
              console.error('Error checking if contact is app user:', error);
            }
            
            appContacts.push({
              id: contact.id || `contact_${Math.random().toString(36).substring(2, 11)}`,
              name: contact.name || 'Unknown',
              avatar: contact.image?.uri,
              phoneNumber: phoneNumber,
              isAppUser: isAppUser,
              userId: userId,
              email: contact.emails && contact.emails.length > 0 ? contact.emails[0].email : undefined
            });
          }
          
          setContacts(appContacts);
          setFilteredContacts(appContacts);
        } else {
          // No contacts found
          setContacts([]);
          setFilteredContacts([]);
        }
      } else {
        // Permission denied
        Alert.alert(
          'Permission Required',
          'Please allow access to your contacts to use this feature.',
          [
            { text: 'Cancel', style: 'cancel' },
            { 
              text: 'Settings', 
              onPress: () => Linking.openSettings() 
            }
          ]
        );
      }
    } catch (error) {
      console.error('Error loading contacts:', error);
      Alert.alert('Error', 'Failed to load contacts. Please try again.');
    } finally {
      setIsContactsLoading(false);
    }
  }, []); // Memoize the function

  const filterContacts = (query: string) => {
    if (!query.trim()) {
      setFilteredContacts(contacts);
      return;
    }
    
    const filtered = contacts.filter((contact: Contact) => 
      contact.name.toLowerCase().includes(query.toLowerCase())
    );
    setFilteredContacts(filtered);
  };

  const handleConversationPress = (conversationId: string) => {
    setSelectedConversationId(conversationId);
    
    // Navigate to the conversation screen
    router.push({
      pathname: '/conversation/[id]',
      params: { id: conversationId }
    });
  };
  
  // Load conversations when component mounts
  useEffect(() => {
    loadConversations();
    
    // Set up interval to refresh conversations every 30 seconds
    const intervalId = setInterval(loadConversations, 30000);
    
    // Clean up interval on unmount
    return () => clearInterval(intervalId);
  }, [loadConversations]); // Make sure loadConversations is memoized with useCallback

  // Add these functions to handle invites and friend management
  const inviteContact = useCallback(async (contact: Contact) => {
    try {
      const user = auth?.user as SupabaseUser | null;
      // Create a shareable link with contact info
      const inviteLink = `https://yourapp.com/invite?ref=${user?.id}&name=${encodeURIComponent(user?.user_metadata?.name || 'A friend')}`;
      
      // Share the invite link
      if (Platform.OS === 'ios') {
        // For iOS, use Share API
        await Share.share({
          message: `Hey ${contact.name}, I'm using this great app for messaging. Join me using this link: ${inviteLink}`,
          url: inviteLink
        });
      } else {
        // For Android, check if we have their phone number
        if (contact.phoneNumber) {
          // Try to send SMS if we have a phone number
          const smsAvailable = await Linking.canOpenURL(`sms:${contact.phoneNumber}`);
          
          if (smsAvailable) {
            await Linking.openURL(
              `sms:${contact.phoneNumber}?body=Hey ${contact.name}, I'm using this great app for messaging. Join me using this link: ${inviteLink}`
            );
          } else {
            // Fallback to general share
            await Share.share({
              message: `Hey ${contact.name}, I'm using this great app for messaging. Join me using this link: ${inviteLink}`,
              title: 'Join me on this app'
            });
          }
        } else {
          // No phone number, use general share
          await Share.share({
            message: `Hey ${contact.name}, I'm using this great app for messaging. Join me using this link: ${inviteLink}`,
            title: 'Join me on this app'
          });
        }
      }
      
      // Show success message
      Alert.alert('Invite Sent', `Invitation sent to ${contact.name}`);
      
    } catch (error) {
      console.error('Error sending invite:', error);
      Alert.alert('Error', 'Failed to send invitation. Please try again.');
    }
  }, [auth?.user]);

  // Function to start a conversation with a contact
  const startConversationWithContact = useCallback(async (contact: Contact) => {
    try {
      if (!auth?.user?.id || !contact.userId) {
        Alert.alert('Error', 'Cannot start conversation. User information is missing.');
        return;
      }
      
      setIsContactsLoading(true);
      
      // Check if a conversation already exists between these users
      const { data: existingConversation, error: checkError } = await supabase
        .from('conversations')
        .select('id')
        .or(`and(participant1_id.eq.${auth.user.id},participant2_id.eq.${contact.userId}),and(participant1_id.eq.${contact.userId},participant2_id.eq.${auth.user.id})`)
        .limit(1);
      
      if (checkError) {
        console.error('Error checking existing conversation:', checkError);
        Alert.alert('Error', 'Failed to check existing conversations. Please try again.');
        setIsContactsLoading(false);
        return;
      }
      
      let conversationId: string;
      
      if (existingConversation && existingConversation.length > 0) {
        // Use existing conversation
        conversationId = existingConversation[0].id;
      } else {
        // Create a new conversation
        const { data: newConversation, error: createError } = await supabase
          .from('conversations')
          .insert({
            participant1_id: auth.user.id,
            participant2_id: contact.userId,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            unread_count: 0
          })
          .select();
        
        if (createError || !newConversation || newConversation.length === 0) {
          console.error('Error creating conversation:', createError);
          Alert.alert('Error', 'Failed to create conversation. Please try again.');
          setIsContactsLoading(false);
          return;
        }
        
        conversationId = newConversation[0].id;
      }
      
      // Close the modal and navigate to the conversation
      setShowNewMessageModal(false);
      
      // Navigate to the conversation
      router.push({
        pathname: '/conversation/[id]',
        params: { id: conversationId }
      });
      
    } catch (error) {
      console.error('Error starting conversation:', error);
      Alert.alert('Error', 'Failed to start conversation. Please try again.');
    } finally {
      setIsContactsLoading(false);
    }
  }, [auth?.user?.id, router]);
  
  // Add this useEffect to load contacts when the modal opens
  useEffect(() => {
    if (showNewMessageModal) {
      loadContacts();
    }
  }, [showNewMessageModal, loadContacts]);
  
  return (
    <View style={styles.container}>
      {/* Header with safe area insets */}
      <BlurView intensity={90} tint="dark" style={styles.header}>
        <View style={styles.headerContent}>
          <Text style={[styles.headerTitle, { color: '#FFFFFF' }]}>Messages</Text>
          <View style={styles.headerButtons}>
            {/* Search button */}
            <TouchableOpacity 
              style={styles.headerButton}
              onPress={() => setIsSearching(!isSearching)}
            >
              <IconSymbol size={dimensions.iconSize * 0.8} name="magnifyingglass" color={Colors.primary} />
            </TouchableOpacity>
            {/* New message button */}
            <TouchableOpacity 
              style={styles.headerButton}
              onPress={() => setShowNewMessageModal(true)}
            >
              <IconSymbol
              size={dimensions.iconSize * 0.8}
              name="square.and.pencil"
              color={Colors.primary}
            />
            </TouchableOpacity>
          </View>
        </View>
      </BlurView>
      
      {/* Search bar */}
      {isSearching && (
        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <IconSymbol size={dimensions.iconSize * 0.7} name="magnifyingglass" color="#8E8E93" />
            <TextInput
              style={styles.searchInput}
              placeholder="Search conversations"
              placeholderTextColor="#8E8E93"
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoFocus
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <IconSymbol size={dimensions.iconSize * 0.7} name="xmark.circle.fill" color="#8E8E93" />
              </TouchableOpacity>
            )}
          </View>
        </View>
      )}
      
      {/* Main content */}
      {isLoadingConversations ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={getDynamicColor('tint')} />
          <Text style={styles.loadingText}>Loading conversations...</Text>
        </View>
      ) : (
        <FlatList
          data={conversationsUI.filter(conv => 
            searchQuery ? conv.name.toLowerCase().includes(searchQuery.toLowerCase()) : true
          )}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={styles.conversationItem}
              onPress={() => handleConversationPress(item.id)}
            >
              <View style={styles.avatarContainer}>
                <Image 
                  source={{ uri: item.avatar || 'https://via.placeholder.com/150' }} 
                  style={styles.avatar} 
                />
                {item.isOnline && <View style={styles.onlineIndicator} />}
              </View>
              <View style={styles.conversationDetails}>
                <View style={styles.conversationHeader}>
                  <Text style={styles.conversationName} numberOfLines={1}>{item.name}</Text>
                  <Text style={styles.conversationTime}>{item.time}</Text>
                </View>
                <View style={styles.conversationFooter}>
                  <Text style={styles.conversationLastMessage} numberOfLines={1}>
                    {item.lastMessage}
                  </Text>
                  {item.unread > 0 && (
                    <View style={styles.unreadBadge}>
                      <Text style={styles.unreadBadgeText}>{item.unread}</Text>
                    </View>
                  )}
                </View>
              </View>
            </TouchableOpacity>
          )}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <View style={styles.emptyStateContainer}>
              <IconSymbol size={dimensions.iconSize * 2.5} name="message.fill" color={`${getDynamicColor('text')}30`} />
              <Text style={styles.emptyStateText}>No conversations yet</Text>
              <TouchableOpacity 
                style={styles.emptyStateButton}
                onPress={() => setShowNewMessageModal(true)}
              >
                <Text style={styles.emptyStateButtonText}>Start a conversation</Text>
              </TouchableOpacity>
            </View>
          }
          refreshControl={
            <RefreshControl
              refreshing={isLoadingConversations}
              onRefresh={loadConversations}
              tintColor={getDynamicColor('tint')}
            />
          }
        />
      )}
      
      {/* New Message Modal */}
      {showNewMessageModal && (
        <TouchableWithoutFeedback onPress={() => {
          Keyboard.dismiss();
          setShowNewMessageModal(false);
        }}>
        <View style={styles.modalOverlay}>
          <BlurView intensity={90} tint="dark" style={styles.newMessageModal}>
            <View style={styles.newMessageHeader}>
              <TouchableOpacity 
                onPress={() => {
                  Keyboard.dismiss();
                  setShowNewMessageModal(false);
                }}
                style={styles.closeButton}
              >
                <Text style={styles.closeButtonText}>Cancel</Text>
              </TouchableOpacity>
              <Text style={styles.newMessageTitle}>New Message</Text>
              <View style={styles.placeholderButton} />
            </View>
            
            <View style={styles.searchSection}>
              <View style={styles.searchBox}>
                <IconSymbol size={20} name="magnifyingglass" color="#8E8E93" />
                <TextInput
                  style={styles.searchInput}
                  placeholder="Search contacts"
                  placeholderTextColor="#8E8E93"
                  value={searchQuery}
                  onChangeText={(text) => {
                    setSearchQuery(text);
                    filterContacts(text);
                  }}
                  autoFocus
                />
              </View>
              
              {/* Contacts list */}
              {contacts.length === 0 && !isContactsLoading ? (
                <View style={styles.emptyContactsContainer}>
                  <IconSymbol size={40} name="person.crop.circle.badge.exclamationmark" color={getDynamicColor('secondaryText')} />
                  <Text style={styles.emptyContactsText}>
                    No contacts found. Please allow access to your contacts or add new contacts to your device.
                  </Text>
                  <TouchableOpacity 
                    style={styles.permissionButton}
                    onPress={() => {
                      // Try to load contacts again
                      loadContacts();
                      
                      // If that doesn't work, direct to settings
                      if (Platform.OS === 'ios') {
                        Linking.openURL('app-settings:');
                      } else {
                        Linking.openSettings();
                      }
                    }}
                  >
                    <Text style={styles.permissionButtonText}>Grant Permission</Text>
                  </TouchableOpacity>
                </View>
              ) : (
                <FlatList
                  data={filteredContacts}
                  keyExtractor={(item) => item.id}
                  renderItem={({ item }: { item: Contact }) => (
                    <TouchableOpacity 
                      style={styles.userItem}
                      onPress={() => {
                        if (item.isAppUser) {
                          Keyboard.dismiss();
                          // Start conversation with this contact
                          startConversationWithContact(item);
                        } else {
                          // Show invite dialog for non-app users
                          Alert.alert(
                            'Invite to App',
                            `Would you like to invite ${item.name} to join the app?`,
                            [
                              { text: 'Cancel', style: 'cancel' },
                              { text: 'Invite', onPress: () => inviteContact(item) }
                            ]
                          );
                        }
                      }}
                    >
                      {/* Contact avatar */}
                      <Image 
                        source={{ uri: item.avatar || 'https://via.placeholder.com/150' }} 
                        style={styles.contactAvatar} 
                      />
                      
                      {/* Contact details */}
                      <View style={styles.contactDetails}>
                        <Text style={styles.contactName}>{item.name}</Text>
                        <Text style={styles.contactInfo}>
                          {item.isAppUser ? 'App User' : 'Not on App'}
                        </Text>
                      </View>
                      
                      {/* Add/Invite button with updated functionality */}
                      <TouchableOpacity
                        style={[
                          styles.contactActionButton,
                          { backgroundColor: item.isAppUser ? getDynamicColor('tint') : 'transparent' }
                        ]}
                        onPress={() => {
                          if (item.isAppUser) {
                            // Start conversation with this contact
                            startConversationWithContact(item);
                          } else {
                            // Invite contact to app
                            inviteContact(item);
                          }
                        }}
                      >
                        <Text 
                          style={[
                            styles.contactActionButtonText,
                            { color: item.isAppUser ? '#FFFFFF' : getDynamicColor('tint') }
                          ]}
                        >
                          {item.isAppUser ? 'Message' : 'Invite'}
                        </Text>
                      </TouchableOpacity>
                    </TouchableOpacity>
                  )}
                  ListEmptyComponent={
                    !isContactsLoading ? (
                      <Text style={styles.noResultsText}>
                        No contacts match your search.
                      </Text>
                    ) : null
                  }
                />
              )}
            </View>
          </BlurView>
        </View>
        </TouchableWithoutFeedback>
      )}
    </View>
  );
}

// Replace the static styles with a function that creates styles
const createStyles = (insets: any, fontSizes: any, colorScheme: string) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colorScheme === 'dark' ? '#000000' : '#FFFFFF',
  },
  header: {
    paddingTop: insets.top,
    paddingBottom: 10,
    paddingHorizontal: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: fontSizes.large,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    marginLeft: 20,
    padding: 5,
  },
  searchContainer: {
    padding: 10,
    backgroundColor: colorScheme === 'dark' ? '#1C1C1E' : '#F2F2F7',
    borderBottomWidth: 1,
    borderBottomColor: colorScheme === 'dark' ? '#333333' : '#DDDDDD',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colorScheme === 'dark' ? '#2C2C2E' : '#FFFFFF',
    borderRadius: 10,
    paddingHorizontal: 10,
    height: 36,
  },
  searchInput: {
    flex: 1,
    height: 36,
    paddingHorizontal: 10,
    color: colorScheme === 'dark' ? '#FFFFFF' : '#000000',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: fontSizes.medium,
    color: colorScheme === 'dark' ? '#AAAAAA' : '#666666',
  },
  conversationItem: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colorScheme === 'dark' ? '#333333' : '#EEEEEE',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#E1E1E1',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 14,
    height: 14,
    borderRadius: 7,
    backgroundColor: '#4CD964',
    borderWidth: 2,
    borderColor: colorScheme === 'dark' ? '#000000' : '#FFFFFF',
  },
  conversationDetails: {
    flex: 1,
    justifyContent: 'center',
  },
  conversationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  conversationName: {
    fontSize: fontSizes.medium,
    fontWeight: '600',
    color: colorScheme === 'dark' ? '#FFFFFF' : '#000000',
    flex: 1,
  },
  conversationTime: {
    fontSize: fontSizes.small,
    color: colorScheme === 'dark' ? '#AAAAAA' : '#666666',
  },
  conversationFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  conversationLastMessage: {
    fontSize: fontSizes.small,
    color: colorScheme === 'dark' ? '#AAAAAA' : '#666666',
    flex: 1,
  },
  unreadBadge: {
    backgroundColor: '#FF3B30',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  unreadBadgeText: {
    color: '#FFFFFF',
    fontSize: fontSizes.small - 2,
    fontWeight: '600',
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    marginTop: 100,
  },
  emptyStateText: {
    fontSize: fontSizes.medium,
    color: colorScheme === 'dark' ? '#AAAAAA' : '#666666',
    marginTop: 16,
    marginBottom: 24,
    textAlign: 'center',
  },
  emptyStateButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 20,
  },
  emptyStateButtonText: {
    color: '#FFFFFF',
    fontSize: fontSizes.medium,
    fontWeight: '600',
  },
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  newMessageModal: {
    backgroundColor: colorScheme === 'dark' ? '#1C1C1E' : '#F2F2F7',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: Dimensions.get('window').height * 0.8,
    overflow: 'hidden',
  },
  newMessageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colorScheme === 'dark' ? '#333333' : '#DDDDDD',
  },
  closeButton: {
    padding: 5,
  },
  closeButtonText: {
    color: Colors.primary,
    fontSize: fontSizes.medium,
  },
  newMessageTitle: {
    fontSize: fontSizes.medium,
    fontWeight: '600',
    color: colorScheme === 'dark' ? '#FFFFFF' : '#000000',
  },
  placeholderButton: {
    width: 60,
  },
  searchSection: {
    flex: 1,
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colorScheme === 'dark' ? '#2C2C2E' : '#FFFFFF',
    margin: 16,
    borderRadius: 10,
    paddingHorizontal: 10,
    height: 40,
  },
  userItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colorScheme === 'dark' ? '#333333' : '#EEEEEE',
  },
  contactAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E1E1E1',
  },
  contactDetails: {
    flex: 1,
    marginLeft: 12,
  },
  contactName: {
    fontSize: fontSizes.medium,
    fontWeight: '500',
    color: colorScheme === 'dark' ? '#FFFFFF' : '#000000',
    marginBottom: 2,
  },
  contactInfo: {
    fontSize: fontSizes.small,
    color: colorScheme === 'dark' ? '#AAAAAA' : '#666666',
  },
  contactActionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  contactActionButtonText: {
    fontSize: fontSizes.small,
    fontWeight: '500',
  },
  emptyContactsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyContactsText: {
    fontSize: fontSizes.medium,
    color: colorScheme === 'dark' ? '#AAAAAA' : '#666666',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  permissionButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 20,
  },
  permissionButtonText: {
    color: '#FFFFFF',
    fontSize: fontSizes.medium,
    fontWeight: '600',
  },
  noResultsText: {
    fontSize: fontSizes.medium,
    color: colorScheme === 'dark' ? '#AAAAAA' : '#666666',
    textAlign: 'center',
    padding: 20,
  },
});








