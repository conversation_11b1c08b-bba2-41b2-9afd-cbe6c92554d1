const fs = require('fs');
const path = require('path');

const root = process.cwd();
const filesToRemove = [
  'google-services.json',
  'GoogleService-Info.plist',
  'services/firebase_mock.ts',
  'types/firebase.d.ts'
];

// Delete Firebase configuration files
filesToRemove.forEach(file => {
  const filePath = path.join(root, file);
  if (fs.existsSync(filePath)) {
    fs.unlinkSync(filePath);
    console.log(`Deleted Firebase file: ${file}`);
  }
});

console.log('Firebase removal completed successfully!');