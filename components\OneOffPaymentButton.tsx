import { useState } from 'react';
import { ActivityIndicator, Alert, StyleSheet, Text, TouchableOpacity } from 'react-native';
import { Colors } from '../constants/Colors';

// Mock implementation for Expo Go
const mockRazorpayCheckout = {
  open: (options: any) => {
    return new Promise((resolve, reject) => {
      // Simulate a successful payment after 1 second
      setTimeout(() => {
        resolve({
          razorpay_payment_id: 'mock_payment_id_' + Date.now(),
          razorpay_order_id: options.order_id,
          razorpay_signature: 'mock_signature_' + Date.now()
        });
      }, 1000);
    });
  }
};

interface OneOffPaymentButtonProps {
  amount: number;
  currency?: string;
  description: string;
  buttonText: string;
  metadata?: Record<string, any>;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export default function OneOffPaymentButton({
  amount,
  currency = 'INR',
  description,
  buttonText,
  metadata = {},
  onSuccess,
  onCancel
}: OneOffPaymentButtonProps) {
  const [loading, setLoading] = useState(false);

  const handlePayment = async () => {
    try {
      setLoading(true);
      
      // Mock order creation
      const order = {
        id: 'order_' + Date.now(),
        amount: amount * 100
      };
      
      const options = {
        description: description,
        image: 'https://your-app-logo-url.png',
        currency: currency,
        key: 'rzp_test_mock_key',
        amount: order.amount,
        name: 'Heatwaves App',
        order_id: order.id,
        prefill: {
          email: '<EMAIL>',
          contact: '9999999999',
          name: 'Heatwaves User'
        },
        theme: { color: Colors.primary }
      };
      
      // Use mock implementation
      mockRazorpayCheckout.open(options)
        .then((data: any) => {
          // Simulate successful payment verification
          Alert.alert('Success', 'Your payment was successful!');
          onSuccess?.();
        })
        .catch((error: any) => {
          Alert.alert('Error', `Payment failed: ${error}`);
          onCancel?.();
        });
    } catch (error) {
      console.error('Payment error:', error);
      Alert.alert('Error', 'There was a problem processing your payment. Please try again.');
      onCancel?.();
    } finally {
      setLoading(false);
    }
  };

  return (
    <TouchableOpacity
      style={[styles.button, loading && styles.buttonDisabled]}
      onPress={handlePayment}
      disabled={loading}
    >
      {loading ? (
        <ActivityIndicator color="#FFFFFF" size="small" />
      ) : (
        <Text style={styles.buttonText}>{buttonText}</Text>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    backgroundColor: Colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonDisabled: {
    opacity: 0.7,
  },
  buttonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

