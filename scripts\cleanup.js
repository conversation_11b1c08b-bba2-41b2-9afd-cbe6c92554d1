const fs = require('fs');
const path = require('path');

const root = process.cwd();
const duplicateFiles = [
  'services/firebase_mock.ts',
  'google-services.json',
  'GoogleService-Info.plist',
  'types/firebase.d.ts'
];

const unnecessaryDirs = [
  'app/services',
  'app/components',
  'app/utils',
];

// Delete duplicate files
duplicateFiles.forEach(file => {
  const filePath = path.join(root, file);
  if (fs.existsSync(filePath)) {
    fs.unlinkSync(filePath);
    console.log(`Deleted duplicate file: ${file}`);
  }
});

// Delete unnecessary directories
unnecessaryDirs.forEach(dir => {
  const dirPath = path.join(root, dir);
  if (fs.existsSync(dirPath)) {
    fs.rmSync(dirPath, { recursive: true, force: true });
    console.log(`Deleted unnecessary directory: ${dir}`);
  }
});

console.log('Cleanup completed successfully!');
