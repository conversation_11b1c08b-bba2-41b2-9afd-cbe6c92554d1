import { useRouter } from 'expo-router';
import { useState } from 'react';
import {
  Dimensions,
  FlatList,
  Image,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { IconSymbol } from '../components/ui/IconSymbol';
import { useAuth } from '../context/AuthContext';

// Mock data for saved profiles
const MOCK_SAVED_PROFILES = [
  {
    id: '1',
    name: '<PERSON>',
    age: 28,
    distance: '3 miles away',
    bio: 'Passionate about photography and outdoor adventures. Looking for someone to explore the city with!',
    avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=634&q=80',
    interests: ['Photography', 'Hiking', 'Travel']
  },
  {
    id: '2',
    name: '<PERSON>',
    age: 31,
    distance: '5 miles away',
    bio: 'Software engineer by day, chef by night. Love trying new recipes and exploring food markets.',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=634&q=80',
    interests: ['Cooking', 'Technology', 'Music']
  },
  {
    id: '3',
    name: 'Sophia Rodriguez',
    age: 26,
    distance: '1 mile away',
    bio: 'Yoga instructor and wellness enthusiast. Seeking genuine connections and positive vibes.',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=634&q=80',
    interests: ['Yoga', 'Meditation', 'Health']
  },
  {
    id: '4',
    name: 'James Taylor',
    age: 30,
    distance: '7 miles away',
    bio: 'Music producer and guitarist. Always looking for new sounds and inspiration.',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&w=634&q=80',
    interests: ['Music', 'Concerts', 'Art']
  }
];

export default function SavedProfilesScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const [savedProfiles, setSavedProfiles] = useState(MOCK_SAVED_PROFILES);
  const { width } = Dimensions.get('window');
  const cardWidth = (width - 48) / 2; // Two cards per row with spacing

  const handleViewProfile = (id: string) => {
    router.push({
      pathname: '/profile-details/[id]',
      params: { id }
    });
  };

  const handleRemoveProfile = (id: string) => {
    setSavedProfiles(savedProfiles.filter(profile => profile.id !== id));
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <IconSymbol name="chevron.left" size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Saved Profiles</Text>
        <View style={styles.placeholder} />
      </View>

      {savedProfiles.length > 0 ? (
        <FlatList
          data={savedProfiles}
          keyExtractor={(item) => item.id}
          numColumns={2}
          contentContainerStyle={styles.listContent}
          renderItem={({ item }) => (
            <TouchableOpacity 
              style={[styles.profileCard, { width: cardWidth }]}
              onPress={() => handleViewProfile(item.id)}
            >
              <Image source={{ uri: item.avatar }} style={styles.profileImage} />
              
              <TouchableOpacity 
                style={styles.removeButton}
                onPress={() => handleRemoveProfile(item.id)}
              >
                <IconSymbol name="xmark" size={16} color="#FFFFFF" />
              </TouchableOpacity>
              
              <View style={styles.profileInfo}>
                <Text style={styles.nameAge}>{item.name}, {item.age}</Text>
                <Text style={styles.distance}>{item.distance}</Text>
                
                <View style={styles.interestsContainer}>
                  {item.interests.slice(0, 2).map((interest, index) => (
                    <View key={index} style={styles.interestBadge}>
                      <Text style={styles.interestText}>{interest}</Text>
                    </View>
                  ))}
                  {item.interests.length > 2 && (
                    <Text style={styles.moreInterests}>+{item.interests.length - 2}</Text>
                  )}
                </View>
              </View>
            </TouchableOpacity>
          )}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <IconSymbol name="heart" size={60} color="#CCCCCC" />
          <Text style={styles.emptyText}>No saved profiles</Text>
          <Text style={styles.emptySubtext}>Profiles you save will appear here</Text>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  placeholder: {
    width: 40,
  },
  listContent: {
    padding: 16,
  },
  profileCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: 16,
    marginHorizontal: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    overflow: 'hidden',
  },
  profileImage: {
    width: '100%',
    height: 180,
    resizeMode: 'cover',
  },
  removeButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileInfo: {
    padding: 12,
  },
  nameAge: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  distance: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 8,
  },
  interestsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  interestBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: '#F5F5F5',
    borderRadius: 12,
    marginRight: 4,
    marginBottom: 4,
  },
  interestText: {
    fontSize: 10,
    color: '#333333',
  },
  moreInterests: {
    fontSize: 10,
    color: '#666666',
    marginLeft: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
  },
});

