import { BlurView } from 'expo-blur';
import React, { useState } from 'react';
import {
  Keyboard,
  KeyboardAvoidingView,
  Modal,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View
} from 'react-native';

interface MessageInputModalProps {
  visible: boolean;
  title: string;
  message: string;
  defaultValue?: string;
  onCancel: () => void;
  onSubmit: (message: string) => void;
}

const MessageInputModal: React.FC<MessageInputModalProps> = ({
  visible,
  title,
  message,
  defaultValue = '',
  onCancel,
  onSubmit
}) => {
  const [inputText, setInputText] = useState(defaultValue);
  
  const handleSubmit = () => {
    onSubmit(inputText);
    setInputText('');
  };
  
  const handleCancel = () => {
    setInputText('');
    onCancel();
  };
  
  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={handleCancel}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.container}
        >
          <View style={styles.modalOverlay}>
            <BlurView intensity={90} tint="dark" style={styles.blurView}>
              <View style={styles.modalContent}>
                <Text style={styles.title}>{title}</Text>
                <Text style={styles.message}>{message}</Text>
                
                <TextInput
                  style={styles.input}
                  value={inputText}
                  onChangeText={setInputText}
                  multiline
                  numberOfLines={3}
                  placeholder="Type your message here..."
                  placeholderTextColor="#999"
                  autoFocus
                />
                
                <View style={styles.buttonContainer}>
                  <TouchableOpacity
                    style={[styles.button, styles.cancelButton]}
                    onPress={handleCancel}
                  >
                    <Text style={styles.cancelButtonText}>Cancel</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[styles.button, styles.submitButton]}
                    onPress={handleSubmit}
                  >
                    <Text style={styles.submitButtonText}>Send</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </BlurView>
          </View>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  blurView: {
    borderRadius: 16,
    overflow: 'hidden',
    width: '85%',
    maxWidth: 400,
  },
  modalContent: {
    padding: 20,
    borderRadius: 16,
    backgroundColor: 'rgba(30, 30, 30, 0.8)',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 10,
  },
  message: {
    fontSize: 16,
    color: '#ddd',
    marginBottom: 20,
  },
  input: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
    padding: 12,
    color: '#fff',
    fontSize: 16,
    marginBottom: 20,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  button: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginLeft: 10,
  },
  cancelButton: {
    backgroundColor: 'rgba(150, 150, 150, 0.3)',
  },
  submitButton: {
    backgroundColor: '#FF3B5C',
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default MessageInputModal;