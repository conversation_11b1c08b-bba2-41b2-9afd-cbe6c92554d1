import { useRouter } from 'expo-router';
import { useState } from 'react';
import {
  <PERSON>ert,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { IconSymbol } from '../components/ui/IconSymbol';

export default function PrivacyScreen() {
  const router = useRouter();
  const [locationSharing, setLocationSharing] = useState(true);
  const [profileVisibility, setProfileVisibility] = useState('everyone');
  const [readReceipts, setReadReceipts] = useState(true);
  const [onlineStatus, setOnlineStatus] = useState(true);
  const [blockList, setBlockList] = useState([]);

  const handleProfileVisibility = (option: string) => {
    setProfileVisibility(option);
  };

  const handleBlockedUsers = () => {
    if (blockList.length === 0) {
      Alert.alert('Blocked Users', 'You haven\'t blocked any users yet.');
    } else {
      // Navigate to blocked users list
      Alert.alert('Blocked Users', 'This feature is coming soon.');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <IconSymbol name="chevron.left" size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Privacy</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content}>
        <Text style={styles.sectionTitle}>Privacy Settings</Text>
        
        <View style={styles.settingItem}>
          <View style={styles.settingTextContainer}>
            <Text style={styles.settingLabel}>Location Sharing</Text>
            <Text style={styles.settingDescription}>Allow the app to use your location</Text>
          </View>
          <Switch
            value={locationSharing}
            onValueChange={setLocationSharing}
            trackColor={{ false: '#D1D1D6', true: '#FF4B00' }}
            thumbColor="#FFFFFF"
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingTextContainer}>
            <Text style={styles.settingLabel}>Read Receipts</Text>
            <Text style={styles.settingDescription}>Let others know when you've read their messages</Text>
          </View>
          <Switch
            value={readReceipts}
            onValueChange={setReadReceipts}
            trackColor={{ false: '#D1D1D6', true: '#FF4B00' }}
            thumbColor="#FFFFFF"
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingTextContainer}>
            <Text style={styles.settingLabel}>Online Status</Text>
            <Text style={styles.settingDescription}>Show when you're active on the app</Text>
          </View>
          <Switch
            value={onlineStatus}
            onValueChange={setOnlineStatus}
            trackColor={{ false: '#D1D1D6', true: '#FF4B00' }}
            thumbColor="#FFFFFF"
          />
        </View>
        
        <Text style={[styles.sectionTitle, styles.secondSection]}>Profile Visibility</Text>
        
        <TouchableOpacity 
          style={[styles.visibilityOption, profileVisibility === 'everyone' && styles.selectedOption]}
          onPress={() => handleProfileVisibility('everyone')}
        >
          <Text style={styles.visibilityOptionText}>Everyone</Text>
          {profileVisibility === 'everyone' && (
            <IconSymbol name="checkmark" size={20} color="#FF4B00" />
          )}
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.visibilityOption, profileVisibility === 'friends' && styles.selectedOption]}
          onPress={() => handleProfileVisibility('friends')}
        >
          <Text style={styles.visibilityOptionText}>Friends Only</Text>
          {profileVisibility === 'friends' && (
            <IconSymbol name="checkmark" size={20} color="#FF4B00" />
          )}
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.visibilityOption, profileVisibility === 'nobody' && styles.selectedOption]}
          onPress={() => handleProfileVisibility('nobody')}
        >
          <Text style={styles.visibilityOptionText}>Nobody</Text>
          {profileVisibility === 'nobody' && (
            <IconSymbol name="checkmark" size={20} color="#FF4B00" />
          )}
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.blockedUsersButton}
          onPress={handleBlockedUsers}
        >
          <Text style={styles.blockedUsersText}>Blocked Users</Text>
          <IconSymbol name="chevron.right" size={20} color="#666" />
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  backButton: {
    padding: 8,
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
  },
  secondSection: {
    marginTop: 30,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  settingTextContainer: {
    flex: 1,
    marginRight: 10,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
  },
  visibilityOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  selectedOption: {
    backgroundColor: 'rgba(255, 75, 0, 0.05)',
  },
  visibilityOptionText: {
    fontSize: 16,
    color: '#333',
  },
  blockedUsersButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 30,
    paddingVertical: 15,
    borderWidth: 1,
    borderColor: '#F0F0F0',
    borderRadius: 10,
    paddingHorizontal: 15,
  },
  blockedUsersText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
});
