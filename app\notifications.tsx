import { useRouter } from 'expo-router';
import { useState } from 'react';
import {
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { IconSymbol } from '../components/ui/IconSymbol';

export default function NotificationsScreen() {
  const router = useRouter();
  const [newMatches, setNewMatches] = useState(true);
  const [messages, setMessages] = useState(true);
  const [friendRequests, setFriendRequests] = useState(true);
  const [appUpdates, setAppUpdates] = useState(false);
  const [promotions, setPromotions] = useState(false);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <IconSymbol name="chevron.left" size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Notifications</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.content}>
        <Text style={styles.sectionTitle}>Notification Settings</Text>
        
        <View style={styles.settingItem}>
          <View style={styles.settingTextContainer}>
            <Text style={styles.settingLabel}>New Matches</Text>
            <Text style={styles.settingDescription}>Get notified when you match with someone</Text>
          </View>
          <Switch
            value={newMatches}
            onValueChange={setNewMatches}
            trackColor={{ false: '#D1D1D6', true: '#FF4B00' }}
            thumbColor="#FFFFFF"
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingTextContainer}>
            <Text style={styles.settingLabel}>Messages</Text>
            <Text style={styles.settingDescription}>Get notified when you receive a message</Text>
          </View>
          <Switch
            value={messages}
            onValueChange={setMessages}
            trackColor={{ false: '#D1D1D6', true: '#FF4B00' }}
            thumbColor="#FFFFFF"
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingTextContainer}>
            <Text style={styles.settingLabel}>Friend Requests</Text>
            <Text style={styles.settingDescription}>Get notified when you receive a friend request</Text>
          </View>
          <Switch
            value={friendRequests}
            onValueChange={setFriendRequests}
            trackColor={{ false: '#D1D1D6', true: '#FF4B00' }}
            thumbColor="#FFFFFF"
          />
        </View>
        
        <Text style={[styles.sectionTitle, styles.secondSection]}>Marketing</Text>
        
        <View style={styles.settingItem}>
          <View style={styles.settingTextContainer}>
            <Text style={styles.settingLabel}>App Updates</Text>
            <Text style={styles.settingDescription}>Get notified about new app features</Text>
          </View>
          <Switch
            value={appUpdates}
            onValueChange={setAppUpdates}
            trackColor={{ false: '#D1D1D6', true: '#FF4B00' }}
            thumbColor="#FFFFFF"
          />
        </View>
        
        <View style={styles.settingItem}>
          <View style={styles.settingTextContainer}>
            <Text style={styles.settingLabel}>Promotions</Text>
            <Text style={styles.settingDescription}>Get notified about special offers</Text>
          </View>
          <Switch
            value={promotions}
            onValueChange={setPromotions}
            trackColor={{ false: '#D1D1D6', true: '#FF4B00' }}
            thumbColor="#FFFFFF"
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  backButton: {
    padding: 8,
  },
  headerRight: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
  },
  secondSection: {
    marginTop: 30,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  settingTextContainer: {
    flex: 1,
    marginRight: 10,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
  },
});
