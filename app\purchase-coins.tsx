import { useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  Linking,
  Modal,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { IconSymbol } from '../components/ui/IconSymbol';
import { useAuth } from '../context/AuthContext';
import { supabase } from '../lib/supabase';

// Define types
interface CoinPackage {
  id: string;
  name: string;
  coins: number;
  bonusCoins?: number;
  price: number;
  discountedPrice?: number;
  currency: string;
  popular?: boolean;
  bestValue?: boolean;
  offerText?: string;
}

interface PaymentMethod {
  id: string;
  name: string;
  iconName: string;
  iconColor: string;
  scheme: string;
  isAvailable: boolean;
}

export default function PurchaseCoinsScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [coinBalance, setCoinBalance] = useState(0);
  const [selectedPackage, setSelectedPackage] = useState<CoinPackage | null>(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);
  const [showPaymentOptions, setShowPaymentOptions] = useState(false);
  
  // Define coin packages
  const coinPackages: CoinPackage[] = [
    { id: 'starter', name: 'Starter Pack', coins: 100, price: 99, currency: 'INR', offerText: 'SAVE 10%' },
    { id: 'standard', name: 'Standard Pack', coins: 500, bonusCoins: 50, price: 499, discountedPrice: 449, currency: 'INR', popular: true, offerText: 'SAVE 10%' },
    { id: 'premium', name: 'Premium Pack', coins: 1000, bonusCoins: 100, price: 999, discountedPrice: 899, currency: 'INR', bestValue: true, offerText: 'SAVE 10%' },
    { id: 'ultimate', name: 'Ultimate Pack', coins: 2500, bonusCoins: 250, price: 2499, discountedPrice: 2299, currency: 'INR', offerText: 'SAVE 8%' },
  ];
  
  // Define UPI apps with proper icon handling for both platforms
  const [upiApps, setUpiApps] = useState<PaymentMethod[]>([]);

  useEffect(() => {
    const checkUpiAppAvailability = async () => {
      const apps = [
        { 
          id: 'gpay', 
          name: 'Google Pay', 
          iconName: 'logo-google', 
          iconColor: '#4285F4', 
          scheme: 'gpay://' 
        },
        { 
          id: 'paytm', 
          name: 'Paytm', 
          iconName: 'wallet-outline', 
          iconColor: '#00BAF2', 
          scheme: 'paytm://' 
        },
        { 
          id: 'phonepe', 
          name: 'PhonePe', 
          iconName: 'phone-portrait-outline', 
          iconColor: '#5F259F', 
          scheme: 'phonepe://' 
        },
        { 
          id: 'bhim', 
          name: 'BHIM UPI', 
          iconName: 'cash-outline', 
          iconColor: '#00A0E3', 
          scheme: 'bhim://' 
        },
      ];

      const availableApps = await Promise.all(
        apps.map(async (app) => ({
          ...app,
          isAvailable: await Linking.canOpenURL(app.scheme),
        }))
      );
      setUpiApps(availableApps);
    };

    checkUpiAppAvailability();
  }, []);
  
  // Show payment sheet
  const showPaymentSheet = () => {
    setShowPaymentOptions(true);
  };
  
  // Hide payment sheet
  const hidePaymentSheet = () => {
    setShowPaymentOptions(false);
  };
  
  // Handle payment with app
  const handlePaymentWithApp = async () => {
    if (!selectedPackage || !selectedPaymentMethod || !user) {
      Alert.alert('Error', 'Please select a package and payment method');
      return;
    }
    
    // In Expo Go, we'll simulate the payment
    if (Platform.OS === 'web' || !selectedPaymentMethod.isAvailable) {
      Alert.alert(
        'Expo Go Simulation',
        `This would normally open ${selectedPaymentMethod.name} for payment. Would you like to simulate a successful payment?`,
        [
          { 
            text: 'Cancel', 
            style: 'cancel' 
          },
          { 
            text: 'Simulate Payment', 
            onPress: async () => {
              setLoading(true);
              // Simulate a delay
              await new Promise(resolve => setTimeout(resolve, 2000));
              
              // Update the user's coin balance
              const newBalance = coinBalance + selectedPackage.coins + (selectedPackage.bonusCoins || 0);
              
              // Update in Supabase
              const { error } = await supabase
                .from('profiles')
                .update({ coin_balance: newBalance })
                .eq('id', user.id);
              
              if (error) {
                console.error('Error updating coin balance:', error);
                Alert.alert('Error', 'Failed to update coin balance');
              } else {
                setCoinBalance(newBalance);
                Alert.alert(
                  'Purchase Successful',
                  `You've purchased ${selectedPackage.coins} Heat Coins${selectedPackage.bonusCoins ? ` + ${selectedPackage.bonusCoins} bonus` : ''}!`,
                  [{ text: 'OK' }]
                );
              }
              
              setLoading(false);
            }
          }
        ]
      );
      return;
    }
    
    // For real devices, try to open the payment app
    try {
      const supported = await Linking.canOpenURL(selectedPaymentMethod.scheme);
      
      if (!supported) {
        Alert.alert('Error', `${selectedPaymentMethod.name} is not installed on your device`);
        return;
      }
      
      // Create a transaction ID
      const transactionId = `HEAT${Date.now()}`;
      
      // Create a UPI payment URI
      const upiParams = {
        pa: 'heatwaves@ybl', // Replace with your actual UPI ID
        pn: 'Heat Dating App',
        am: (selectedPackage.discountedPrice || selectedPackage.price).toString(),
        tr: transactionId,
        tn: `Purchase ${selectedPackage.coins} Heat Coins${selectedPackage.bonusCoins ? ` + ${selectedPackage.bonusCoins} bonus` : ''}`
      };
      
      // Construct the UPI URI
      const upiUri = `${selectedPaymentMethod.scheme}pay?${Object.entries(upiParams)
        .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
        .join('&')}`;
      
      // Open the UPI app
      await Linking.openURL(upiUri);
      
      // After returning from the payment app, ask the user if payment was successful
      setTimeout(() => {
        Alert.alert(
          'Payment Status',
          'Was your payment successful?',
          [
            { 
              text: 'No', 
              style: 'cancel' 
            },
            { 
              text: 'Yes', 
              onPress: async () => {
                setLoading(true);
                
                // Update the user's coin balance
                const newBalance = coinBalance + selectedPackage.coins + (selectedPackage.bonusCoins || 0);
                
                // Update in Supabase
                const { error } = await supabase
                  .from('profiles')
                  .update({ coin_balance: newBalance })
                  .eq('id', user.id);
                
                if (error) {
                  console.error('Error updating coin balance:', error);
                  Alert.alert('Error', 'Failed to update coin balance');
                } else {
                  setCoinBalance(newBalance);
                  Alert.alert(
                    'Purchase Successful',
                    `You've purchased ${selectedPackage.coins} Heat Coins${selectedPackage.bonusCoins ? ` + ${selectedPackage.bonusCoins} bonus` : ''}!`,
                    [{ text: 'OK' }]
                  );
                }
                
                setLoading(false);
              }
            }
          ]
        );
      }, 3000);
    } catch (error) {
      console.error('Error opening payment app:', error);
      Alert.alert('Error', 'Failed to open payment app');
    }
  };
  
  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      if (user?.id) {
        try {
          setLoading(true);
          const { data, error } = await supabase
            .from('profiles')
            .select('coin_balance')
            .eq('id', user.id)
            .single();
          
          if (error) {
            console.error('Error fetching user data:', error);
          } else {
            setCoinBalance(data.coin_balance || 0);
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
        } finally {
          setLoading(false);
        }
      }
    };
    
    fetchUserData();
  }, [user]);
  
  return (
    <View style={styles.container}>
      {/* Header */}
      <SafeAreaView edges={['top']} style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <IconSymbol name="chevron.left" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        
        <Text style={styles.headerTitle}>Buy Heat Coins</Text>
        
        <View style={styles.coinBalance}>
          <IconSymbol name="flame.fill" size={16} color="#FF6B00" />
          <Text style={styles.coinBalanceText}>{coinBalance}</Text>
        </View>
      </SafeAreaView>
      
      {/* Main Content */}
      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        {/* Coin Packages Section */}
        <Text style={styles.sectionTitle}>Select a Package</Text>
        
        <View style={styles.packagesContainer}>
          {coinPackages.map((pkg: CoinPackage) => (
            <TouchableOpacity
              key={pkg.id}
              style={[
                styles.coinPackage,
                selectedPackage?.id === pkg.id && styles.selectedPackage
              ]}
              onPress={() => setSelectedPackage(pkg)}
            >
              <View style={styles.coinPackageContent}>
                <View style={styles.coinPackageHeader}>
                  <View style={styles.coinPackageIcon}>
                    <IconSymbol name="flame.fill" size={24} color="#FFF" />
                  </View>
                  <Text style={styles.coinAmount}>{pkg.coins}</Text>
                  <Text style={styles.coinLabel}>Heat Coins</Text>
                  {pkg.bonusCoins && pkg.bonusCoins > 0 && (
                    <View style={styles.bonusCoinsContainer}>
                      <Text style={styles.bonusCoinsText}>+{pkg.bonusCoins} BONUS</Text>
                    </View>
                  )}
                </View>
                
                <View style={styles.coinPackageInfo}>
                  <Text style={styles.coinPackageTitle}>{pkg.name}</Text>
                  {pkg.discountedPrice ? (
                    <View style={styles.priceContainer}>
                      <Text style={styles.originalPrice}>₹{pkg.price}</Text>
                      <Text style={styles.coinPackagePrice}>₹{pkg.discountedPrice}</Text>
                    </View>
                  ) : (
                    <Text style={styles.coinPackagePrice}>₹{pkg.price}</Text>
                  )}
                  {pkg.offerText && (
                    <Text style={styles.offerText}>{pkg.offerText}</Text>
                  )}
                </View>
              </View>
              
              {pkg.popular && (
                <View style={styles.popularBadge}>
                  <Text style={styles.popularBadgeText}>MOST POPULAR</Text>
                </View>
              )}
              
              {pkg.bestValue && (
                <View style={[styles.popularBadge, styles.bestValueBadge]}>
                  <Text style={styles.popularBadgeText}>BEST VALUE</Text>
                </View>
              )}
              
              {selectedPackage?.id === pkg.id && (
                <View style={styles.checkmark}>
                  <IconSymbol name="checkmark.circle.fill" size={24} color="#FF6B00" />
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>
        
        {/* Info Section */}
        <View style={styles.infoSection}>
          <Text style={styles.infoTitle}>What are Heat Coins?</Text>
          <Text style={styles.infoText}>
            Heat Coins are the in-app currency that lets you unlock premium features, 
            boost your profile visibility, and send special gifts to your matches.
          </Text>
        </View>
      </ScrollView>
      
      {/* Footer */}
      <SafeAreaView edges={['bottom']} style={styles.footer}>
        {/* Payment method selection */}
        <TouchableOpacity 
          style={styles.paymentSelector}
          onPress={showPaymentSheet}
        >
          <View style={styles.selectedPayment}>
            {selectedPaymentMethod ? (
              <>
                <View style={[styles.paymentMethodIconSmall, { backgroundColor: selectedPaymentMethod.iconColor + '20' }]}>
                  <IconSymbol 
                    name={selectedPaymentMethod.iconName} 
                    size={20} 
                    color={selectedPaymentMethod.iconColor} 
                  />
                </View>
                <Text style={styles.selectedPaymentText}>{selectedPaymentMethod.name}</Text>
              </>
            ) : (
              <Text style={styles.selectPaymentText}>Select Payment Method</Text>
            )}
            <IconSymbol name="chevron.right" size={16} color="#FFFFFF" />
          </View>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[
            styles.buyButton,
            (!selectedPackage || !selectedPaymentMethod) && styles.disabledButton
          ]}
          onPress={handlePaymentWithApp}
          disabled={loading || !selectedPackage || !selectedPaymentMethod}
        >
          {loading ? (
            <ActivityIndicator color="#FFFFFF" size="small" />
          ) : (
            <Text style={styles.buyButtonText}>
              Buy Now • ₹{selectedPackage ? (selectedPackage.discountedPrice || selectedPackage.price) : '0'}
            </Text>
          )}
        </TouchableOpacity>
      </SafeAreaView>
      
      {/* Payment Method Sheet */}
      <Modal
        visible={showPaymentOptions}
        animationType="slide"
        transparent={true}
        onRequestClose={hidePaymentSheet}
      >
        <View style={styles.modalContainer}>
          <TouchableOpacity 
            style={styles.paymentSheetBackdrop}
            activeOpacity={1}
            onPress={hidePaymentSheet}
          >
            <View></View>
          </TouchableOpacity>
          
          <View style={styles.paymentSheet}>
            <View style={styles.paymentSheetHeader}>
              <View style={styles.paymentSheetHandle} />
              <Text style={styles.paymentSheetTitle}>Select Payment Method</Text>
              <TouchableOpacity onPress={hidePaymentSheet}>
                <IconSymbol name="xmark" size={24} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.paymentSheetContent}>
              {/* Payment options */}
              {upiApps.map((app) => (
                <TouchableOpacity 
                  key={app.id}
                  style={[
                    styles.paymentOption,
                    selectedPaymentMethod?.id === app.id && styles.selectedPaymentOption,
                    !app.isAvailable && styles.disabledPaymentOption
                  ]}
                  onPress={() => {
                    setSelectedPaymentMethod(app);
                    hidePaymentSheet();
                  }}
                  disabled={!app.isAvailable}
                >
                  <View style={[styles.paymentMethodIcon, { backgroundColor: app.iconColor + '20' }]}>
                    <IconSymbol 
                      name={app.iconName} 
                      size={24} 
                      color={app.iconColor} 
                    />
                  </View>
                  <View style={styles.paymentOptionInfo}>
                    <Text style={styles.paymentOptionName}>{app.name}</Text>
                    {!app.isAvailable && (
                      <Text style={styles.notInstalledText}>Not installed</Text>
                    )}
                  </View>
                  {selectedPaymentMethod?.id === app.id && (
                    <IconSymbol name="checkmark" size={20} color="#FF6B00" />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const { width, height } = Dimensions.get('window');

// Define styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#121212',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#1A1A1A',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  coinBalance: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 107, 0, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  coinBalanceText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
    marginLeft: 4,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 16,
  },
  packagesContainer: {
    gap: 16,
  },
  coinPackage: {
    backgroundColor: '#1A1A1A',
    borderRadius: 16,
    padding: 16,
    borderWidth: 2,
    borderColor: 'transparent',
    position: 'relative',
    overflow: 'hidden',
  },
  selectedPackage: {
    borderColor: '#FF6B00',
  },
  coinPackageContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  coinPackageHeader: {
    alignItems: 'center',
    marginRight: 16,
  },
  coinPackageIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FF6B00',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  coinAmount: {
    fontSize: 20,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  coinLabel: {
    fontSize: 12,
    color: '#AAAAAA',
  },
  bonusCoinsContainer: {
    backgroundColor: '#FF6B00',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginTop: 4,
  },
  bonusCoinsText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  coinPackageInfo: {
    flex: 1,
  },
  coinPackageTitle: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  originalPrice: {
    color: '#FFFFFF',
    fontSize: 14,
    opacity: 0.7,
    textDecorationLine: 'line-through',
    marginRight: 8,
  },
  coinPackagePrice: {
    color: '#FFFFFF',
    fontSize: 14,
    opacity: 0.8,
    marginTop: 4,
  },
  checkmark: {
    marginLeft: 8,
  },
  paymentMethodsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  paymentMethod: {
    width: '48%',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    alignItems: 'center',
  },
  disabledPaymentMethod: {
    opacity: 0.6,
  },
  paymentMethodIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  paymentMethodName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  notInstalledText: {
    fontSize: 12,
    color: '#999',
  },
  infoSection: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 12,
    padding: 16,
    marginTop: 8,
    marginBottom: 32,
  },
  infoTitle: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  infoText: {
    color: '#FFFFFF',
    opacity: 0.8,
    lineHeight: 20,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  blurView: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  loadingContainer: {
    backgroundColor: 'rgba(18, 18, 18, 0.8)',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
  },
  loadingText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 16,
  },
  footer: {
    backgroundColor: '#121212',
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  paymentSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
    padding: 12,
    flex: 1,
    marginRight: 16,
  },
  selectedPayment: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentMethodIconSmall: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  selectedPaymentText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
  },
  selectPaymentText: {
    color: '#FFFFFF',
    fontSize: 16,
    opacity: 0.8,
    flex: 1,
  },
  buyButton: {
    backgroundColor: '#FF6B00',
    borderRadius: 8,
    padding: 12,
    flex: 1,
  },
  buyButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  paymentSheetOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  paymentSheetBackdrop: {
    flex: 1,
  },
  paymentSheet: {
    backgroundColor: '#1A1A1A',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 16,
  },
  paymentSheetHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  paymentSheetHandle: {
    width: 40,
    height: 4,
    backgroundColor: '#FFFFFF',
    opacity: 0.3,
    borderRadius: 2,
    alignSelf: 'center',
  },
  paymentSheetTitle: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  paymentSheetContent: {
    maxHeight: height * 0.6,
  },
  paymentOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  paymentMethodIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  paymentOptionInfo: {
    flex: 1,
  },
  paymentOptionName: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  selectedPaymentOption: {
    backgroundColor: 'rgba(255, 107, 0, 0.2)',
    borderWidth: 1,
    borderColor: '#FF6B00',
  },
  disabledPaymentOption: {
    opacity: 0.6,
  },
  offerText: {
    color: '#FF6B00',
    fontSize: 12,
    fontWeight: 'bold',
    marginTop: 4,
  },
  popularBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: '#FF6B00',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderBottomLeftRadius: 8,
  },
  popularBadgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  disabledButton: {
    opacity: 0.5,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  bestValueBadge: {
    backgroundColor: '#4CAF50', // Green color for best value
  },
});






