import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity,
  Linking
} from 'react-native';
import { useRouter } from 'expo-router';
import { IconSymbol } from '../../components/ui/IconSymbol';
import { Colors } from '../../constants/Colors';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export default function HelpScreen() {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  
  const faqItems = [
    {
      question: 'How do I edit my profile?',
      answer: 'You can edit your profile by going to the Profile tab and tapping on the "Edit Profile" button.'
    },
    {
      question: 'How do I change my password?',
      answer: 'To change your password, go to Profile > Settings > Security > Change Password.'
    },
    {
      question: 'How do I delete my account?',
      answer: 'To delete your account, go to Profile > Settings > Account > Delete Account. Please note that this action is irreversible.'
    },
    {
      question: 'How do location services work?',
      answer: 'Location services help you find people and events near you. You can enable or disable this feature in your profile settings.'
    },
    {
      question: 'How do I report inappropriate behavior?',
      answer: 'You can report inappropriate behavior by going to the user\'s profile and tapping on the "Report" button.'
    }
  ];
  
  const contactSupport = () => {
    Linking.openURL('mailto:<EMAIL>');
  };
  
  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <IconSymbol name="chevron.left" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Help Center</Text>
        <View style={{ width: 40 }} />
      </View>
      
      <ScrollView style={styles.content}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Frequently Asked Questions</Text>
          
          {faqItems.map((item, index) => (
            <View key={index} style={styles.faqItem}>
              <Text style={styles.question}>{item.question}</Text>
              <Text style={styles.answer}>{item.answer}</Text>
            </View>
          ))}
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Contact Support</Text>
          <Text style={styles.supportText}>
            Need more help? Our support team is always ready to assist you.
          </Text>
          
          <TouchableOpacity style={styles.supportButton} onPress={contactSupport}>
            <IconSymbol name="envelope.fill" size={20} color="#FFFFFF" />
            <Text style={styles.supportButtonText}>Email Support</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    color: Colors.text,
  },
  faqItem: {
    marginBottom: 16,
    padding: 16,
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
  },
  question: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
    color: Colors.text,
  },
  answer: {
    fontSize: 14,
    lineHeight: 20,
    color: '#666',
  },
  supportText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#666',
    marginBottom: 16,
  },
  supportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  supportButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    marginLeft: 8,
  },
});