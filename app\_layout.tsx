import { Stack, useRouter, useSegments } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useEffect } from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { AuthProvider, useAuth } from '../context/AuthContext';
import { useResponsiveStyles } from '../hooks/useResponsiveStyles';

// This component handles redirecting users based on auth state
function RootLayoutNav() {
  const { user, loading } = useAuth();
  const segments = useSegments();
  const router = useRouter();
  const { isLandscape } = useResponsiveStyles();

  useEffect(() => {
    if (loading) return;

    const inAuthGroup = segments[0] === '(auth)';
    const inTabsGroup = segments[0] === '(tabs)';
    
    // Check if we're on the edit-profile page - safely handle segments array
    let isEditProfilePage = false;
    // Use type assertion to tell TypeScript that segments is a string array
    const segmentsArray = segments as string[];
    if (segmentsArray.length > 1) {
      isEditProfilePage = segmentsArray[1] === 'edit-profile';
    }
    
    // Only redirect from auth to tabs if not on edit-profile
    if (!user && !inAuthGroup) {
      // If user is not signed in and not on an auth screen, redirect to welcome
      router.replace('/(auth)');
    } else if (user && inAuthGroup && !isEditProfilePage) {
      // If user is signed in and on an auth screen (except edit-profile), redirect to home
      router.replace('/(tabs)');
    }
  }, [user, loading, segments]);

  return (
    <SafeAreaProvider>
      <StatusBar style="dark" />
      <Stack 
        screenOptions={{ 
          headerShown: false,
          animation: isLandscape ? 'fade' : 'default',
          contentStyle: { 
            flex: 1,
          }
        }}
      >
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen name="(auth)" options={{ headerShown: false }} />
        <Stack.Screen name="venue/[id]" options={{ headerShown: false }} />
        <Stack.Screen name="conversation/[id]" options={{ headerShown: false }} />
        <Stack.Screen name="profile-details" options={{ headerShown: false }} />
      </Stack>
    </SafeAreaProvider>
  );
}

export default function RootLayout() {
  return (
    <AuthProvider>
      <RootLayoutNav />
    </AuthProvider>
  );
}










