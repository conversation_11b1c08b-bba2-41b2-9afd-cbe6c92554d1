import React from 'react';
import { View, Text } from 'react-native';

// Mock Camera component
export const Camera = {
  Constants: {
    Type: {
      back: 'back',
      front: 'front'
    },
    FlashMode: {
      on: 'on',
      off: 'off',
      auto: 'auto',
      torch: 'torch'
    }
  },
  requestCameraPermissionsAsync: async () => ({ 
    status: 'granted',
    granted: true,
    expires: 'never',
    canAskAgain: true
  }),
  requestMicrophonePermissionsAsync: async () => ({ 
    status: 'granted',
    granted: true,
    expires: 'never',
    canAskAgain: true
  })
};

// Mock Camera component for rendering
export default function CameraComponent({ style, type, flashMode, onCameraReady }: any) {
  return (
    <View style={[{ backgroundColor: '#000', justifyContent: 'center', alignItems: 'center' }, style]}>
      <Text style={{ color: '#fff' }}>Camera Preview (Mock)</Text>
    </View>
  );
}
