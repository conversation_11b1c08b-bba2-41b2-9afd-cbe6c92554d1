import { useLocalSearchParams, useRouter } from 'expo-router';
import { useEffect, useState } from 'react';
import {
  Alert,
  Dimensions,
  Image,
  Linking,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  useColorScheme
} from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { IconSymbol } from '../../components/ui/IconSymbol';
import { ThemedText } from '../../components/ui/ThemedText';

// Define Venue type
interface Venue {
  id: string;
  name: string;
  description: string;
  address: string;
  photos: string[];
  categories: string[];
  rating?: number;
  priceLevel?: number;
  hours?: string;
  isOpen?: boolean;
  latitude?: number;
  longitude?: number;
  distance?: string;
  reviewCount?: number;
}

const VenueDetailScreen = () => {
  const params = useLocalSearchParams();
  const router = useRouter();
  const [venue, setVenue] = useState<Venue | null>(null);
  const [activePhotoIndex, setActivePhotoIndex] = useState(0);
  const [isPlanning, setIsPlanning] = useState(false);
  const [peopleGoing, setPeopleGoing] = useState(Math.floor(Math.random() * 20) + 5);
  const colorScheme = useColorScheme();
  const { width } = Dimensions.get('window');

  // Define theme colors
  const themeColors = {
    tint: '#007AFF',
    background: '#FFFFFF',
    text: '#000000',
    border: '#E0E0E0',
    card: '#FFFFFF',
    secondaryText: '#8E8E93'
  };

  useEffect(() => {
    // Create venue object from URL params
    try {
      const venueData: Venue = {
        id: params.id as string,
        name: params.name as string,
        description: params.description as string,
        address: params.address as string,
        photos: params.photos ? JSON.parse(params.photos as string) : [],
        categories: [params.category as string],
        rating: params.rating ? parseFloat(params.rating as string) : undefined,
        priceLevel: params.priceLevel ? parseInt(params.priceLevel as string, 10) : undefined,
        hours: params.hours as string,
        isOpen: params.isOpen === "true",
        latitude: params.latitude ? parseFloat(params.latitude as string) : undefined,
        longitude: params.longitude ? parseFloat(params.longitude as string) : undefined,
        distance: params.distance as string
      };
      
      setVenue(venueData);
    } catch (error) {
      console.error("Error parsing venue data:", error);
      Alert.alert("Error", "Could not load venue details");
    }
  }, [params]);

  // Handle opening maps
  const handleOpenMaps = () => {
    if (!venue) return;
    
    // Use full address and name for more accurate location
    const query = encodeURIComponent(`${venue.name}, ${venue.address}`);
    
    // For iOS, use coordinates if available for more precision
    const mapsUrl = Platform.select({
      ios: venue.latitude && venue.longitude 
        ? `maps:?q=${venue.name}&ll=${venue.latitude},${venue.longitude}` 
        : `maps:?q=${query}`,
      android: venue.latitude && venue.longitude 
        ? `geo:${venue.latitude},${venue.longitude}?q=${query}` 
        : `geo:0,0?q=${query}`,
    });
    
    if (mapsUrl) {
      Linking.openURL(mapsUrl).catch(err => {
        console.error('Error opening maps:', err);
        Alert.alert(
          'Navigation Error',
          'Unable to open maps. Please make sure you have a maps app installed.',
          [{ text: 'OK' }]
        );
      });
    }
  };

  // Handle inviting friends
  const handleInviteFriends = () => {
    if (!venue) return;
    
    router.push({
      pathname: '/invite-friends',
      params: {
        placeId: venue.id,
        placeName: venue.name,
        placeCategory: venue.categories[0],
        placeAddress: venue.address,
        placePhoto: venue.photos && venue.photos.length > 0 ? venue.photos[0] : ""
      }
    });
  };

  // Render price level
  const renderPriceLevel = (level?: number) => {
    if (!level) return '';
    
    let price = '';
    for (let i = 0; i < level; i++) {
      price += '$';
    }
    return price;
  };

  if (!venue) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading venue details...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header with back button */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <IconSymbol name="chevron.left" size={24} color="#000000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Venue Details</Text>
        <View style={{ width: 40 }} />
      </View>
      
      <ScrollView 
        style={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContentContainer}
      >
        {/* Photo gallery */}
        <View style={styles.photoGallery}>
          <ScrollView
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            onMomentumScrollEnd={(e) => {
              const newIndex = Math.round(e.nativeEvent.contentOffset.x / width);
              setActivePhotoIndex(newIndex);
            }}
          >
            {venue.photos.length > 0 ? (
              venue.photos.map((photo, index) => (
                <Image 
                  key={index}
                  source={{ uri: photo }}
                  style={[styles.venuePhoto, { width }]}
                />
              ))
            ) : (
              <View style={[styles.noPhotoContainer, { width }]}>
                <IconSymbol name="photo" size={60} color="#CCCCCC" />
                <Text style={styles.noPhotoText}>No photos available</Text>
              </View>
            )}
          </ScrollView>
          
          {/* Photo indicators */}
          {venue.photos.length > 1 && (
            <View style={styles.photoIndicators}>
              {venue.photos.map((_, index) => (
                <View 
                  key={index} 
                  style={[
                    styles.photoIndicator, 
                    index === activePhotoIndex && styles.photoIndicatorActive
                  ]} 
                />
              ))}
            </View>
          )}
        </View>
        
        {/* Venue details */}
        <View style={styles.venueDetails}>
          <ThemedText style={styles.venueName}>{venue.name}</ThemedText>
          
          <View style={styles.venueCategories}>
            {venue.categories.map((category, index) => (
              <View key={index} style={styles.categoryPill}>
                <ThemedText style={styles.categoryText}>{category}</ThemedText>
              </View>
            ))}
          </View>
          
          <View style={styles.venueMetadata}>
            {venue.rating && (
              <View style={styles.ratingContainer}>
                {Platform.OS === 'ios' ? (
                  <IconSymbol name="star.fill" size={16} color="#FFD700" />
                ) : (
                  <Ionicons name="star" size={16} color="#FFD700" />
                )}
                <Text style={styles.ratingText}>{venue.rating.toFixed(1)}</Text>
                <Text style={styles.reviewCount}>({venue.reviewCount || '120'} reviews)</Text>
              </View>
            )}
            
            {venue.priceLevel && (
              <Text style={styles.priceText}>{renderPriceLevel(venue.priceLevel)}</Text>
            )}
            
            {venue.distance && (
              <Text style={styles.distanceText}>{venue.distance}</Text>
            )}
          </View>
          
          <View style={styles.addressContainer}>
            {Platform.OS === 'ios' ? (
              <IconSymbol name="location.fill" size={16} color="#999999" />
            ) : (
              <Ionicons name="location" size={16} color="#999999" />
            )}
            <Text style={styles.addressText}>{venue.address}</Text>
          </View>
          
          {venue.hours && (
            <View style={styles.hoursContainer}>
              <IconSymbol name="clock.fill" size={16} color="#4A89F3" />
              <Text style={styles.hoursText}>
                {venue.hours} · {venue.isOpen ? 'Open Now' : 'Closed'}
              </Text>
            </View>
          )}
          
          <Text style={styles.descriptionText}>{venue.description}</Text>
          
          {/* Action buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity 
              style={styles.inviteButton}
              onPress={handleInviteFriends}
            >
              <IconSymbol name="person.2.fill" size={20} color="#FFFFFF" />
              <Text style={styles.buttonText}>Invite Friends</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.directionsButton}
              onPress={handleOpenMaps}
            >
              <IconSymbol name="location.fill" size={20} color="#FFFFFF" />
              <Text style={styles.buttonText}>Get Directions</Text>
            </TouchableOpacity>
          </View>
          
          {/* People going section */}
          <View style={styles.peopleGoingContainer}>
            <Text style={styles.peopleGoingTitle}>
              {peopleGoing} people are planning to visit
            </Text>
            
            <TouchableOpacity 
              style={[
                styles.planButton, 
                isPlanning && styles.planButtonActive
              ]}
              onPress={() => setIsPlanning(!isPlanning)}
            >
              <Text style={[
                styles.planButtonText,
                isPlanning && styles.planButtonTextActive
              ]}>
                {isPlanning ? "I'm Going" : "I'm Interested"}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const getStyles = () => {
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#FFFFFF',
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 16,
      paddingTop: Platform.OS === 'ios' ? 60 : 40,
      paddingBottom: 10,
      backgroundColor: '#FFFFFF',
    },
    backButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      alignItems: 'center',
      justifyContent: 'center',
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: '600',
    },
    scrollContent: {
      flex: 1,
    },
    scrollContentContainer: {
      paddingBottom: 40,
    },
    photoGallery: {
      position: 'relative',
      height: 300,
    },
    venuePhoto: {
      height: 300,
      resizeMode: 'cover',
    },
    noPhotoContainer: {
      height: 300,
      backgroundColor: '#F5F5F5',
      alignItems: 'center',
      justifyContent: 'center',
    },
    noPhotoText: {
      marginTop: 10,
      color: '#999999',
    },
    photoIndicators: {
      position: 'absolute',
      bottom: 16,
      left: 0,
      right: 0,
      flexDirection: 'row',
      justifyContent: 'center',
    },
    photoIndicator: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: 'rgba(255, 255, 255, 0.5)',
      marginHorizontal: 4,
    },
    photoIndicatorActive: {
      backgroundColor: '#FFFFFF',
      width: 12,
      height: 12,
      borderRadius: 6,
    },
    venueDetails: {
      padding: 16,
    },
    venueName: {
      fontSize: 24,
      fontWeight: 'bold',
      marginBottom: 8,
    },
    venueCategories: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginBottom: 12,
    },
    categoryPill: {
      backgroundColor: 'rgba(255, 75, 0, 0.1)',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      marginRight: 8,
      marginBottom: 8,
    },
    categoryText: {
      color: '#FF4B00',
      fontSize: 14,
      fontWeight: '500',
    },
    venueMetadata: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
    },
    ratingContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: 16,
    },
    ratingText: {
      marginLeft: 4,
      fontSize: 16,
      fontWeight: '500',
    },
    priceText: {
      fontSize: 16,
      fontWeight: '500',
      marginRight: 16,
    },
    distanceText: {
      fontSize: 16,
      color: '#666666',
    },
    addressContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 8,
    },
    addressText: {
      marginLeft: 8,
      fontSize: 16,
      color: '#333333',
      flex: 1,
    },
    hoursContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 16,
    },
    hoursText: {
      marginLeft: 8,
      fontSize: 16,
      color: '#333333',
    },
    descriptionText: {
      fontSize: 16,
      lineHeight: 24,
      color: '#333333',
      marginBottom: 24,
    },
    actionButtons: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 24,
    },
    inviteButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: '#FF4B00',
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 24,
      flex: 1,
      marginRight: 8,
    },
    directionsButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: '#4A89F3',
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 24,
      flex: 1,
      marginLeft: 8,
    },
    buttonText: {
      color: '#FFFFFF',
      fontWeight: '600',
      fontSize: 16,
      marginLeft: 8,
    },
    peopleGoingContainer: {
      backgroundColor: '#F5F5F5',
      borderRadius: 16,
      padding: 16,
      alignItems: 'center',
    },
    peopleGoingTitle: {
      fontSize: 16,
      fontWeight: '500',
      marginBottom: 12,
    },
    planButton: {
      paddingVertical: 10,
      paddingHorizontal: 24,
      borderRadius: 20,
      borderWidth: 1,
      borderColor: '#FF4B00',
      backgroundColor: 'transparent',
    },
    planButtonActive: {
      backgroundColor: '#FF4B00',
    },
    planButtonText: {
      color: '#FF4B00',
      fontWeight: '600',
      fontSize: 16,
    },
    planButtonTextActive: {
      color: '#FFFFFF',
    },
    reviewCount: {
      marginLeft: 4,
      fontSize: 14,
      color: '#666666',
    },
  });
};

const styles = getStyles();

export default VenueDetailScreen;




