import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';

interface VideoCallUIProps {
  callState: 'connecting' | 'ringing' | 'ongoing' | 'ended';
  formatDuration: () => string;
  onAccept: () => void;
  onDecline: () => void;
  onEnd: () => void;
  onMute: () => void;
  onVideo: () => void;
  onSwitchCamera: () => void;
  localStreamUrl: string | null;
  remoteStreamUrl: string | null;
}

export function VideoCallUI({
  callState,
  formatDuration,
  onAccept,
  onDecline,
  onEnd,
  onMute,
  onVideo,
  onSwitchCamera,
  localStreamUrl,
  remoteStreamUrl
}: VideoCallUIProps) {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.videoContainer}>
        {remoteStreamUrl ? (
          <Image source={{ uri: remoteStreamUrl }} style={styles.remoteVideo} />
        ) : (
          <View style={styles.placeholderVideo}>
            <Text style={styles.placeholderText}>Connecting video...</Text>
          </View>
        )}
        
        {localStreamUrl && (
          <View style={styles.localVideoContainer}>
            <Image source={{ uri: localStreamUrl }} style={styles.localVideo} />
          </View>
        )}
      </View>
      
      <View style={styles.callInfo}>
        <Text style={styles.callStatus}>
          {callState === 'connecting' && 'Connecting...'}
          {callState === 'ringing' && 'Ringing...'}
          {callState === 'ongoing' && formatDuration()}
          {callState === 'ended' && 'Call ended'}
        </Text>
      </View>
      
      <View style={styles.controls}>
        {callState === 'ringing' ? (
          <>
            <TouchableOpacity style={[styles.controlButton, styles.declineButton]} onPress={onDecline}>
              <Ionicons name="close" size={30} color="#FFFFFF" />
            </TouchableOpacity>
            <TouchableOpacity style={[styles.controlButton, styles.acceptButton]} onPress={onAccept}>
              <Ionicons name="videocam" size={30} color="#FFFFFF" />
            </TouchableOpacity>
          </>
        ) : (
          <>
            <TouchableOpacity style={styles.controlButton} onPress={onMute}>
              <Ionicons name="mic-off" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.controlButton} onPress={onVideo}>
              <Ionicons name="videocam-off" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.controlButton} onPress={onSwitchCamera}>
              <Ionicons name="camera-reverse" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            <TouchableOpacity style={[styles.controlButton, styles.endButton]} onPress={onEnd}>
              <Ionicons name="call" size={24} color="#FFFFFF" />
            </TouchableOpacity>
          </>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  videoContainer: {
    flex: 1,
    position: 'relative',
  },
  remoteVideo: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  placeholderVideo: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#333333',
  },
  placeholderText: {
    color: '#FFFFFF',
    fontSize: 18,
  },
  localVideoContainer: {
    position: 'absolute',
    top: 20,
    right: 20,
    width: 100,
    height: 150,
    borderRadius: 10,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  localVideo: {
    width: '100%',
    height: '100%',
  },
  callInfo: {
    padding: 20,
    alignItems: 'center',
  },
  callStatus: {
    color: '#FFFFFF',
    fontSize: 16,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 20,
  },
  controlButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#555555',
    justifyContent: 'center',
    alignItems: 'center',
  },
  acceptButton: {
    backgroundColor: '#4CAF50',
  },
  declineButton: {
    backgroundColor: '#F44336',
  },
  endButton: {
    backgroundColor: '#F44336',
  },
});