import { Stack } from 'expo-router';
import { useAuth } from '../../context/AuthContext';

export default function AuthLayout() {
  const { user } = useAuth();

  return (
    <Stack
      screenOptions={{
        headerShown: false,
        contentStyle: {
          backgroundColor: '#ffffff',
        },
        // Only disable gesture navigation for non-authenticated screens
        gestureEnabled: !!user,
      }}
    >
      <Stack.Screen 
        name="edit-profile"
        options={{
          // Allow gesture navigation for edit-profile
          gestureEnabled: true,
        }}
      />
    </Stack>
  );
}



